/**
 * Tesseract OCR Engine
 * Comprehensive OCR implementation using Tesseract.js for text extraction from images and scanned PDFs
 */

import Tesseract, { createWorker, createScheduler, Worker, Scheduler } from 'tesseract.js';

export interface OCRWord {
  text: string;
  confidence: number;
  bbox: {
    x0: number;
    y0: number;
    x1: number;
    y1: number;
  };
  baseline: {
    x0: number;
    y0: number;
    x1: number;
    y1: number;
  };
}

export interface OCRLine {
  text: string;
  confidence: number;
  bbox: {
    x0: number;
    y0: number;
    x1: number;
    y1: number;
  };
  words: OCRWord[];
}

export interface OCRParagraph {
  text: string;
  confidence: number;
  bbox: {
    x0: number;
    y0: number;
    x1: number;
    y1: number;
  };
  lines: OCRLine[];
}

export interface OCRResult {
  id: string;
  pageNumber: number;
  text: string;
  confidence: number;
  language: string;
  detectedLanguages?: string[];
  paragraphs: OCRParagraph[];
  lines: OCRLine[];
  words: OCRWord[];
  processingTime: number;
  imageInfo: {
    width: number;
    height: number;
    channels: number;
  };
  metadata: {
    orientation?: number;
    rotationAngle?: number;
    skewAngle?: number;
    textDirection?: 'ltr' | 'rtl' | 'ttb';
  };
}

export interface OCRProgress {
  status: 'initializing' | 'loading' | 'processing' | 'complete' | 'error';
  progress: number;
  message: string;
  pageNumber?: number;
  totalPages?: number;
}

export interface OCRConfig {
  language: string | string[];
  engineMode: Tesseract.OEM;
  pageSegMode: Tesseract.PSM;
  whitelist?: string;
  blacklist?: string;
  preserveInterwordSpaces?: boolean;
  tessjs_create_hocr?: boolean;
  tessjs_create_tsv?: boolean;
  tessjs_create_box?: boolean;
  tessjs_create_unlv?: boolean;
  tessjs_create_osd?: boolean;
}

export interface ImagePreprocessingOptions {
  enhanceContrast?: boolean;
  denoiseImage?: boolean;
  sharpenImage?: boolean;
  adjustBrightness?: number; // -100 to 100
  adjustContrast?: number; // -100 to 100
  binarizeThreshold?: number; // 0 to 255
  scaleImage?: number; // Scale factor
  rotateImage?: number; // Rotation angle in degrees
  cropImage?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

const DEFAULT_OCR_CONFIG: OCRConfig = {
  language: 'eng',
  engineMode: Tesseract.OEM.LSTM_ONLY,
  pageSegMode: Tesseract.PSM.AUTO,
  preserveInterwordSpaces: true,
  tessjs_create_hocr: false,
  tessjs_create_tsv: false,
  tessjs_create_box: false,
  tessjs_create_unlv: false,
  tessjs_create_osd: false,
};

export class TesseractEngine {
  private workers: Worker[] = [];
  private scheduler: Scheduler | null = null;
  private isInitialized = false;
  private maxWorkers: number;
  private config: OCRConfig;

  constructor(maxWorkers: number = 2, config: Partial<OCRConfig> = {}) {
    this.maxWorkers = maxWorkers;
    this.config = { ...DEFAULT_OCR_CONFIG, ...config };
  }

  /**
   * Initialize the OCR engine with workers
   */
  async initialize(onProgress?: (progress: OCRProgress) => void): Promise<void> {
    if (this.isInitialized) return;

    try {
      onProgress?.({
        status: 'initializing',
        progress: 0,
        message: 'Initializing OCR engine...',
      });

      // Create scheduler for managing workers
      this.scheduler = createScheduler();

      // Create and initialize workers
      for (let i = 0; i < this.maxWorkers; i++) {
        onProgress?.({
          status: 'loading',
          progress: (i / this.maxWorkers) * 50,
          message: `Initializing worker ${i + 1}/${this.maxWorkers}...`,
        });

        const worker = await createWorker(this.config.language, 1, {
          logger: (m) => {
            if (m.status === 'loading tesseract core' || m.status === 'loading language traineddata') {
              onProgress?.({
                status: 'loading',
                progress: 50 + (m.progress * 50) / this.maxWorkers,
                message: `${m.status}: ${Math.round(m.progress * 100)}%`,
              });
            }
          },
        });

        // Configure worker
        await worker.setParameters(this.config);
        
        this.workers.push(worker);
        this.scheduler.addWorker(worker);
      }

      this.isInitialized = true;

      onProgress?.({
        status: 'complete',
        progress: 100,
        message: 'OCR engine initialized successfully',
      });
    } catch (error) {
      onProgress?.({
        status: 'error',
        progress: 0,
        message: `Failed to initialize OCR engine: ${error instanceof Error ? error.message : 'Unknown error'}`,
      });
      throw error;
    }
  }

  /**
   * Process a single image with OCR
   */
  async processImage(
    imageData: string | ImageData | HTMLCanvasElement | HTMLImageElement,
    options: {
      pageNumber?: number;
      preprocessingOptions?: ImagePreprocessingOptions;
      config?: Partial<OCRConfig>;
      onProgress?: (progress: OCRProgress) => void;
    } = {}
  ): Promise<OCRResult> {
    if (!this.isInitialized || !this.scheduler) {
      throw new Error('OCR engine not initialized. Call initialize() first.');
    }

    const { pageNumber = 1, preprocessingOptions, config, onProgress } = options;
    const startTime = Date.now();

    try {
      onProgress?.({
        status: 'processing',
        progress: 0,
        message: `Processing page ${pageNumber}...`,
        pageNumber,
      });

      // Preprocess image if options provided
      let processedImage = imageData;
      if (preprocessingOptions) {
        processedImage = await this.preprocessImage(imageData, preprocessingOptions);
      }

      // Merge config with instance config
      const mergedConfig = { ...this.config, ...config };

      // Perform OCR
      const result = await this.scheduler.addJob('recognize', processedImage, {
        ...mergedConfig,
        logger: (m) => {
          if (m.status === 'recognizing text') {
            onProgress?.({
              status: 'processing',
              progress: m.progress * 100,
              message: `Recognizing text: ${Math.round(m.progress * 100)}%`,
              pageNumber,
            });
          }
        },
      });

      const processingTime = Math.max(1, Date.now() - startTime); // Ensure at least 1ms

      // Extract structured data
      const ocrResult: OCRResult = {
        id: `ocr-${pageNumber}-${Date.now()}`,
        pageNumber,
        text: result.data.text,
        confidence: result.data.confidence,
        language: Array.isArray(mergedConfig.language)
          ? mergedConfig.language[0]
          : mergedConfig.language,
        paragraphs: this.extractParagraphs(result.data),
        lines: this.extractLines(result.data),
        words: this.extractWords(result.data),
        processingTime,
        imageInfo: {
          width: result.data.imageWidth || 0,
          height: result.data.imageHeight || 0,
          channels: 3, // Assuming RGB
        },
        metadata: {
          orientation: result.data.osd?.orientation,
          rotationAngle: result.data.osd?.rotate,
          skewAngle: result.data.osd?.skew,
          textDirection: result.data.osd?.script_dir === 0 ? 'ltr' : 'rtl',
        },
      };

      onProgress?.({
        status: 'complete',
        progress: 100,
        message: `Page ${pageNumber} processed successfully`,
        pageNumber,
      });

      return ocrResult;
    } catch (error) {
      onProgress?.({
        status: 'error',
        progress: 0,
        message: `Failed to process page ${pageNumber}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        pageNumber,
      });
      throw error;
    }
  }

  /**
   * Process multiple images in batch
   */
  async processBatch(
    images: Array<{
      data: string | ImageData | HTMLCanvasElement | HTMLImageElement;
      pageNumber: number;
      preprocessingOptions?: ImagePreprocessingOptions;
    }>,
    options: {
      config?: Partial<OCRConfig>;
      onProgress?: (progress: OCRProgress) => void;
      onPageComplete?: (result: OCRResult) => void;
    } = {}
  ): Promise<OCRResult[]> {
    const { config, onProgress, onPageComplete } = options;
    const results: OCRResult[] = [];
    const totalImages = images.length;

    onProgress?.({
      status: 'processing',
      progress: 0,
      message: `Processing ${totalImages} pages...`,
      totalPages: totalImages,
    });

    for (let i = 0; i < images.length; i++) {
      const image = images[i];
      
      try {
        const result = await this.processImage(image.data, {
          pageNumber: image.pageNumber,
          preprocessingOptions: image.preprocessingOptions,
          config,
          onProgress: (pageProgress) => {
            const overallProgress = ((i / totalImages) * 100) + ((pageProgress.progress / totalImages));
            onProgress?.({
              ...pageProgress,
              progress: overallProgress,
              totalPages: totalImages,
            });
          },
        });

        results.push(result);
        onPageComplete?.(result);

        onProgress?.({
          status: 'processing',
          progress: ((i + 1) / totalImages) * 100,
          message: `Completed ${i + 1}/${totalImages} pages`,
          pageNumber: image.pageNumber,
          totalPages: totalImages,
        });
      } catch (error) {
        console.error(`Failed to process page ${image.pageNumber}:`, error);
        // Continue with other pages
      }
    }

    onProgress?.({
      status: 'complete',
      progress: 100,
      message: `Batch processing complete: ${results.length}/${totalImages} pages processed`,
      totalPages: totalImages,
    });

    return results;
  }

  /**
   * Preprocess image to improve OCR accuracy
   */
  private async preprocessImage(
    imageData: string | ImageData | HTMLCanvasElement | HTMLImageElement,
    options: ImagePreprocessingOptions
  ): Promise<HTMLCanvasElement> {
    // Create canvas for image processing
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) throw new Error('Failed to get canvas context');

    // Load image
    let img: HTMLImageElement;
    if (typeof imageData === 'string') {
      img = new Image();
      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
        img.src = imageData;
      });
    } else if (imageData instanceof HTMLImageElement) {
      img = imageData;
    } else {
      // Handle other types by converting to canvas first
      const tempCanvas = document.createElement('canvas');
      const tempCtx = tempCanvas.getContext('2d');
      if (!tempCtx) throw new Error('Failed to get temp canvas context');
      
      if (imageData instanceof HTMLCanvasElement) {
        tempCanvas.width = imageData.width;
        tempCanvas.height = imageData.height;
        tempCtx.drawImage(imageData, 0, 0);
      } else if (imageData instanceof ImageData) {
        tempCanvas.width = imageData.width;
        tempCanvas.height = imageData.height;
        tempCtx.putImageData(imageData, 0, 0);
      }
      
      img = new Image();
      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
        img.src = tempCanvas.toDataURL();
      });
    }

    // Set canvas size
    const scale = options.scaleImage || 1;
    canvas.width = img.width * scale;
    canvas.height = img.height * scale;

    // Apply rotation if specified
    if (options.rotateImage) {
      ctx.translate(canvas.width / 2, canvas.height / 2);
      ctx.rotate((options.rotateImage * Math.PI) / 180);
      ctx.translate(-canvas.width / 2, -canvas.height / 2);
    }

    // Draw image
    ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

    // Apply brightness and contrast adjustments
    if (options.adjustBrightness !== undefined || options.adjustContrast !== undefined) {
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageData.data;

      const brightness = (options.adjustBrightness || 0) * 2.55; // Convert to 0-255 range
      const contrast = ((options.adjustContrast || 0) + 100) / 100; // Convert to multiplier

      for (let i = 0; i < data.length; i += 4) {
        // Apply brightness and contrast to RGB channels
        data[i] = Math.max(0, Math.min(255, (data[i] - 128) * contrast + 128 + brightness));
        data[i + 1] = Math.max(0, Math.min(255, (data[i + 1] - 128) * contrast + 128 + brightness));
        data[i + 2] = Math.max(0, Math.min(255, (data[i + 2] - 128) * contrast + 128 + brightness));
      }

      ctx.putImageData(imageData, 0, 0);
    }

    // Apply cropping if specified
    if (options.cropImage) {
      const { x, y, width, height } = options.cropImage;
      const croppedImageData = ctx.getImageData(x, y, width, height);
      canvas.width = width;
      canvas.height = height;
      ctx.putImageData(croppedImageData, 0, 0);
    }

    return canvas;
  }

  /**
   * Extract paragraphs from Tesseract result
   */
  private extractParagraphs(data: any): OCRParagraph[] {
    if (!data.paragraphs) return [];

    return data.paragraphs.map((para: any) => ({
      text: para.text,
      confidence: para.confidence,
      bbox: para.bbox,
      lines: para.lines?.map((line: any) => ({
        text: line.text,
        confidence: line.confidence,
        bbox: line.bbox,
        words: line.words?.map((word: any) => ({
          text: word.text,
          confidence: word.confidence,
          bbox: word.bbox,
          baseline: word.baseline,
        })) || [],
      })) || [],
    }));
  }

  /**
   * Extract lines from Tesseract result
   */
  private extractLines(data: any): OCRLine[] {
    if (!data.lines) return [];

    return data.lines.map((line: any) => ({
      text: line.text,
      confidence: line.confidence,
      bbox: line.bbox,
      words: line.words?.map((word: any) => ({
        text: word.text,
        confidence: word.confidence,
        bbox: word.bbox,
        baseline: word.baseline,
      })) || [],
    }));
  }

  /**
   * Extract words from Tesseract result
   */
  private extractWords(data: any): OCRWord[] {
    if (!data.words) return [];

    return data.words.map((word: any) => ({
      text: word.text,
      confidence: word.confidence,
      bbox: word.bbox,
      baseline: word.baseline,
    }));
  }

  /**
   * Detect language of the image
   */
  async detectLanguage(
    imageData: string | ImageData | HTMLCanvasElement | HTMLImageElement
  ): Promise<string[]> {
    if (!this.isInitialized || !this.scheduler) {
      throw new Error('OCR engine not initialized');
    }

    try {
      const result = await this.scheduler.addJob('detect', imageData);
      return result.data.languages || [];
    } catch (error) {
      console.error('Language detection failed:', error);
      return [];
    }
  }

  /**
   * Terminate all workers and cleanup
   */
  async terminate(): Promise<void> {
    if (this.scheduler) {
      await this.scheduler.terminate();
      this.scheduler = null;
    }

    for (const worker of this.workers) {
      await worker.terminate();
    }

    this.workers = [];
    this.isInitialized = false;
  }

  /**
   * Get engine status
   */
  getStatus(): {
    isInitialized: boolean;
    workerCount: number;
    config: OCRConfig;
  } {
    return {
      isInitialized: this.isInitialized,
      workerCount: this.workers.length,
      config: this.config,
    };
  }
}
