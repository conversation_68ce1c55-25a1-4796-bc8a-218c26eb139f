/**
 * Advanced Touch Gesture Handler
 * Comprehensive touch gesture recognition for mobile PDF viewing
 */

export interface TouchPoint {
  id: number;
  x: number;
  y: number;
  timestamp: number;
}

export interface GestureEvent {
  type: 'tap' | 'double-tap' | 'long-press' | 'pan' | 'pinch' | 'swipe' | 'rotate';
  startPoint: TouchPoint;
  currentPoint: TouchPoint;
  deltaX: number;
  deltaY: number;
  distance: number;
  scale: number;
  rotation: number;
  velocity: number;
  direction: 'up' | 'down' | 'left' | 'right' | null;
  duration: number;
  preventDefault: () => void;
  stopPropagation: () => void;
}

export interface GestureConfig {
  tapThreshold: number; // max movement for tap (px)
  doubleTapDelay: number; // max time between taps (ms)
  longPressDelay: number; // min time for long press (ms)
  swipeThreshold: number; // min distance for swipe (px)
  swipeVelocityThreshold: number; // min velocity for swipe (px/ms)
  pinchThreshold: number; // min scale change for pinch
  rotationThreshold: number; // min rotation for rotate (degrees)
  panThreshold: number; // min distance for pan (px)
  enableHapticFeedback: boolean;
  preventDefaultTouch: boolean;
  enablePassiveListeners: boolean;
}

export interface GestureCallbacks {
  onTap?: (event: GestureEvent) => void;
  onDoubleTap?: (event: GestureEvent) => void;
  onLongPress?: (event: GestureEvent) => void;
  onPanStart?: (event: GestureEvent) => void;
  onPanMove?: (event: GestureEvent) => void;
  onPanEnd?: (event: GestureEvent) => void;
  onPinchStart?: (event: GestureEvent) => void;
  onPinchMove?: (event: GestureEvent) => void;
  onPinchEnd?: (event: GestureEvent) => void;
  onSwipe?: (event: GestureEvent) => void;
  onRotateStart?: (event: GestureEvent) => void;
  onRotateMove?: (event: GestureEvent) => void;
  onRotateEnd?: (event: GestureEvent) => void;
}

const DEFAULT_CONFIG: GestureConfig = {
  tapThreshold: 10,
  doubleTapDelay: 300,
  longPressDelay: 500,
  swipeThreshold: 50,
  swipeVelocityThreshold: 0.3,
  pinchThreshold: 0.1,
  rotationThreshold: 5,
  panThreshold: 10,
  enableHapticFeedback: true,
  preventDefaultTouch: true,
  enablePassiveListeners: false,
};

export class TouchGestureHandler {
  private element: HTMLElement;
  private config: GestureConfig;
  private callbacks: GestureCallbacks;
  
  private touches: Map<number, TouchPoint> = new Map();
  private lastTap: TouchPoint | null = null;
  private longPressTimer: number | null = null;
  private isGestureActive = false;
  private currentGesture: GestureEvent['type'] | null = null;
  
  private initialDistance = 0;
  private initialAngle = 0;
  private initialScale = 1;
  private initialRotation = 0;

  constructor(
    element: HTMLElement,
    callbacks: GestureCallbacks = {},
    config: Partial<GestureConfig> = {}
  ) {
    this.element = element;
    this.callbacks = callbacks;
    this.config = { ...DEFAULT_CONFIG, ...config };
    
    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    const options = this.config.enablePassiveListeners 
      ? { passive: !this.config.preventDefaultTouch }
      : false;

    this.element.addEventListener('touchstart', this.handleTouchStart.bind(this), options);
    this.element.addEventListener('touchmove', this.handleTouchMove.bind(this), options);
    this.element.addEventListener('touchend', this.handleTouchEnd.bind(this), options);
    this.element.addEventListener('touchcancel', this.handleTouchCancel.bind(this), options);
    
    // Prevent context menu on long press
    this.element.addEventListener('contextmenu', (e) => e.preventDefault());
  }

  private handleTouchStart(event: TouchEvent): void {
    if (this.config.preventDefaultTouch) {
      event.preventDefault();
    }

    const now = Date.now();
    
    // Store touch points
    for (let i = 0; i < event.changedTouches.length; i++) {
      const touch = event.changedTouches[i];
      const touchPoint: TouchPoint = {
        id: touch.identifier,
        x: touch.clientX,
        y: touch.clientY,
        timestamp: now,
      };
      this.touches.set(touch.identifier, touchPoint);
    }

    const touchCount = this.touches.size;

    if (touchCount === 1) {
      this.handleSingleTouchStart(Array.from(this.touches.values())[0]);
    } else if (touchCount === 2) {
      this.handleMultiTouchStart();
    } else {
      this.clearGesture();
    }
  }

  private handleSingleTouchStart(touchPoint: TouchPoint): void {
    // Check for double tap
    if (this.lastTap && 
        touchPoint.timestamp - this.lastTap.timestamp < this.config.doubleTapDelay &&
        this.getDistance(touchPoint, this.lastTap) < this.config.tapThreshold) {
      
      this.triggerDoubleTap(touchPoint);
      this.lastTap = null;
      return;
    }

    // Start long press timer
    this.longPressTimer = window.setTimeout(() => {
      if (this.touches.size === 1) {
        this.triggerLongPress(touchPoint);
      }
    }, this.config.longPressDelay);
  }

  private handleMultiTouchStart(): void {
    this.clearLongPressTimer();
    
    const touchPoints = Array.from(this.touches.values());
    if (touchPoints.length === 2) {
      this.initialDistance = this.getDistance(touchPoints[0], touchPoints[1]);
      this.initialAngle = this.getAngle(touchPoints[0], touchPoints[1]);
      this.initialScale = 1;
      this.initialRotation = 0;
    }
  }

  private handleTouchMove(event: TouchEvent): void {
    if (this.config.preventDefaultTouch) {
      event.preventDefault();
    }

    const now = Date.now();
    
    // Update touch points
    for (let i = 0; i < event.changedTouches.length; i++) {
      const touch = event.changedTouches[i];
      const existingTouch = this.touches.get(touch.identifier);
      
      if (existingTouch) {
        const touchPoint: TouchPoint = {
          id: touch.identifier,
          x: touch.clientX,
          y: touch.clientY,
          timestamp: now,
        };
        this.touches.set(touch.identifier, touchPoint);
      }
    }

    const touchCount = this.touches.size;

    if (touchCount === 1) {
      this.handleSingleTouchMove();
    } else if (touchCount === 2) {
      this.handleMultiTouchMove();
    }
  }

  private handleSingleTouchMove(): void {
    const touchPoints = Array.from(this.touches.values());
    const currentTouch = touchPoints[0];
    
    // Clear long press timer if moved too much
    if (this.longPressTimer) {
      const startTouch = this.getInitialTouch(currentTouch.id);
      if (startTouch && this.getDistance(currentTouch, startTouch) > this.config.tapThreshold) {
        this.clearLongPressTimer();
      }
    }

    // Handle pan gesture
    const startTouch = this.getInitialTouch(currentTouch.id);
    if (startTouch) {
      const distance = this.getDistance(currentTouch, startTouch);
      
      if (distance > this.config.panThreshold) {
        if (this.currentGesture !== 'pan') {
          this.currentGesture = 'pan';
          this.triggerPanStart(startTouch, currentTouch);
        } else {
          this.triggerPanMove(startTouch, currentTouch);
        }
      }
    }
  }

  private handleMultiTouchMove(): void {
    const touchPoints = Array.from(this.touches.values());
    if (touchPoints.length !== 2) return;

    const [touch1, touch2] = touchPoints;
    const currentDistance = this.getDistance(touch1, touch2);
    const currentAngle = this.getAngle(touch1, touch2);
    
    const scale = currentDistance / this.initialDistance;
    const rotation = currentAngle - this.initialAngle;
    
    // Detect pinch gesture
    if (Math.abs(scale - 1) > this.config.pinchThreshold) {
      if (this.currentGesture !== 'pinch') {
        this.currentGesture = 'pinch';
        this.triggerPinchStart(touch1, touch2, scale);
      } else {
        this.triggerPinchMove(touch1, touch2, scale);
      }
    }
    
    // Detect rotation gesture
    if (Math.abs(rotation) > this.config.rotationThreshold) {
      if (this.currentGesture !== 'rotate') {
        this.currentGesture = 'rotate';
        this.triggerRotateStart(touch1, touch2, rotation);
      } else {
        this.triggerRotateMove(touch1, touch2, rotation);
      }
    }
  }

  private handleTouchEnd(event: TouchEvent): void {
    if (this.config.preventDefaultTouch) {
      event.preventDefault();
    }

    const now = Date.now();
    
    // Remove ended touches
    for (let i = 0; i < event.changedTouches.length; i++) {
      const touch = event.changedTouches[i];
      const touchPoint = this.touches.get(touch.identifier);
      
      if (touchPoint) {
        // Check for swipe before removing
        this.checkForSwipe(touchPoint, now);
        this.touches.delete(touch.identifier);
      }
    }

    const remainingTouches = this.touches.size;

    if (remainingTouches === 0) {
      this.handleAllTouchesEnd();
    } else if (remainingTouches === 1 && this.currentGesture === 'pinch') {
      this.triggerPinchEnd();
    } else if (remainingTouches === 1 && this.currentGesture === 'rotate') {
      this.triggerRotateEnd();
    }
  }

  private handleAllTouchesEnd(): void {
    this.clearLongPressTimer();
    
    if (this.currentGesture === 'pan') {
      this.triggerPanEnd();
    } else if (this.currentGesture === 'pinch') {
      this.triggerPinchEnd();
    } else if (this.currentGesture === 'rotate') {
      this.triggerRotateEnd();
    } else if (!this.currentGesture && this.lastTap) {
      // Single tap
      this.triggerTap(this.lastTap);
    }
    
    this.clearGesture();
  }

  private handleTouchCancel(event: TouchEvent): void {
    this.clearGesture();
    this.touches.clear();
  }

  private checkForSwipe(touchPoint: TouchPoint, endTime: number): void {
    const startTouch = this.getInitialTouch(touchPoint.id);
    if (!startTouch) return;

    const distance = this.getDistance(touchPoint, startTouch);
    const duration = endTime - startTouch.timestamp;
    const velocity = distance / duration;

    if (distance > this.config.swipeThreshold && velocity > this.config.swipeVelocityThreshold) {
      this.triggerSwipe(startTouch, touchPoint, velocity);
    }
  }

  private getInitialTouch(touchId: number): TouchPoint | null {
    // In a real implementation, we'd store initial touch positions
    // For now, return the current touch as a placeholder
    return this.touches.get(touchId) || null;
  }

  private getDistance(point1: TouchPoint, point2: TouchPoint): number {
    const dx = point2.x - point1.x;
    const dy = point2.y - point1.y;
    return Math.sqrt(dx * dx + dy * dy);
  }

  private getAngle(point1: TouchPoint, point2: TouchPoint): number {
    return Math.atan2(point2.y - point1.y, point2.x - point1.x) * 180 / Math.PI;
  }

  private getDirection(startPoint: TouchPoint, endPoint: TouchPoint): GestureEvent['direction'] {
    const dx = endPoint.x - startPoint.x;
    const dy = endPoint.y - startPoint.y;
    
    if (Math.abs(dx) > Math.abs(dy)) {
      return dx > 0 ? 'right' : 'left';
    } else {
      return dy > 0 ? 'down' : 'up';
    }
  }

  private createGestureEvent(
    type: GestureEvent['type'],
    startPoint: TouchPoint,
    currentPoint: TouchPoint,
    additionalData: Partial<GestureEvent> = {}
  ): GestureEvent {
    const deltaX = currentPoint.x - startPoint.x;
    const deltaY = currentPoint.y - startPoint.y;
    const distance = this.getDistance(startPoint, currentPoint);
    const duration = currentPoint.timestamp - startPoint.timestamp;
    const velocity = duration > 0 ? distance / duration : 0;

    return {
      type,
      startPoint,
      currentPoint,
      deltaX,
      deltaY,
      distance,
      scale: 1,
      rotation: 0,
      velocity,
      direction: this.getDirection(startPoint, currentPoint),
      duration,
      preventDefault: () => {},
      stopPropagation: () => {},
      ...additionalData,
    };
  }

  private triggerTap(touchPoint: TouchPoint): void {
    const event = this.createGestureEvent('tap', touchPoint, touchPoint);
    this.callbacks.onTap?.(event);
    this.triggerHapticFeedback('light');
    this.lastTap = touchPoint;
  }

  private triggerDoubleTap(touchPoint: TouchPoint): void {
    const event = this.createGestureEvent('double-tap', touchPoint, touchPoint);
    this.callbacks.onDoubleTap?.(event);
    this.triggerHapticFeedback('medium');
  }

  private triggerLongPress(touchPoint: TouchPoint): void {
    const event = this.createGestureEvent('long-press', touchPoint, touchPoint);
    this.callbacks.onLongPress?.(event);
    this.triggerHapticFeedback('heavy');
  }

  private triggerPanStart(startPoint: TouchPoint, currentPoint: TouchPoint): void {
    const event = this.createGestureEvent('pan', startPoint, currentPoint);
    this.callbacks.onPanStart?.(event);
  }

  private triggerPanMove(startPoint: TouchPoint, currentPoint: TouchPoint): void {
    const event = this.createGestureEvent('pan', startPoint, currentPoint);
    this.callbacks.onPanMove?.(event);
  }

  private triggerPanEnd(): void {
    const touchPoints = Array.from(this.touches.values());
    if (touchPoints.length > 0) {
      const currentPoint = touchPoints[0];
      const startPoint = this.getInitialTouch(currentPoint.id) || currentPoint;
      const event = this.createGestureEvent('pan', startPoint, currentPoint);
      this.callbacks.onPanEnd?.(event);
    }
  }

  private triggerPinchStart(touch1: TouchPoint, touch2: TouchPoint, scale: number): void {
    const centerPoint = this.getCenterPoint(touch1, touch2);
    const event = this.createGestureEvent('pinch', centerPoint, centerPoint, { scale });
    this.callbacks.onPinchStart?.(event);
  }

  private triggerPinchMove(touch1: TouchPoint, touch2: TouchPoint, scale: number): void {
    const centerPoint = this.getCenterPoint(touch1, touch2);
    const event = this.createGestureEvent('pinch', centerPoint, centerPoint, { scale });
    this.callbacks.onPinchMove?.(event);
  }

  private triggerPinchEnd(): void {
    const touchPoints = Array.from(this.touches.values());
    if (touchPoints.length >= 1) {
      const event = this.createGestureEvent('pinch', touchPoints[0], touchPoints[0]);
      this.callbacks.onPinchEnd?.(event);
    }
  }

  private triggerSwipe(startPoint: TouchPoint, endPoint: TouchPoint, velocity: number): void {
    const event = this.createGestureEvent('swipe', startPoint, endPoint, { velocity });
    this.callbacks.onSwipe?.(event);
    this.triggerHapticFeedback('light');
  }

  private triggerRotateStart(touch1: TouchPoint, touch2: TouchPoint, rotation: number): void {
    const centerPoint = this.getCenterPoint(touch1, touch2);
    const event = this.createGestureEvent('rotate', centerPoint, centerPoint, { rotation });
    this.callbacks.onRotateStart?.(event);
  }

  private triggerRotateMove(touch1: TouchPoint, touch2: TouchPoint, rotation: number): void {
    const centerPoint = this.getCenterPoint(touch1, touch2);
    const event = this.createGestureEvent('rotate', centerPoint, centerPoint, { rotation });
    this.callbacks.onRotateMove?.(event);
  }

  private triggerRotateEnd(): void {
    const touchPoints = Array.from(this.touches.values());
    if (touchPoints.length >= 1) {
      const event = this.createGestureEvent('rotate', touchPoints[0], touchPoints[0]);
      this.callbacks.onRotateEnd?.(event);
    }
  }

  private getCenterPoint(touch1: TouchPoint, touch2: TouchPoint): TouchPoint {
    return {
      id: -1,
      x: (touch1.x + touch2.x) / 2,
      y: (touch1.y + touch2.y) / 2,
      timestamp: Math.max(touch1.timestamp, touch2.timestamp),
    };
  }

  private triggerHapticFeedback(intensity: 'light' | 'medium' | 'heavy'): void {
    if (!this.config.enableHapticFeedback || !navigator.vibrate) return;

    const patterns = {
      light: [10],
      medium: [20],
      heavy: [30],
    };

    navigator.vibrate(patterns[intensity]);
  }

  private clearLongPressTimer(): void {
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }
  }

  private clearGesture(): void {
    this.currentGesture = null;
    this.isGestureActive = false;
    this.clearLongPressTimer();
  }

  public updateConfig(newConfig: Partial<GestureConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  public updateCallbacks(newCallbacks: Partial<GestureCallbacks>): void {
    this.callbacks = { ...this.callbacks, ...newCallbacks };
  }

  public destroy(): void {
    this.clearGesture();
    this.touches.clear();
    
    // Remove event listeners
    this.element.removeEventListener('touchstart', this.handleTouchStart.bind(this));
    this.element.removeEventListener('touchmove', this.handleTouchMove.bind(this));
    this.element.removeEventListener('touchend', this.handleTouchEnd.bind(this));
    this.element.removeEventListener('touchcancel', this.handleTouchCancel.bind(this));
  }
}
