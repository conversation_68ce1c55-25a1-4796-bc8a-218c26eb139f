/**
 * Visual Accessibility Features
 * High contrast modes, font scaling, and color customization for enhanced visual accessibility
 */

export interface VisualAccessibilityConfig {
  highContrastMode: 'none' | 'light' | 'dark' | 'custom';
  fontScaling: number; // 0.5 to 3.0
  lineHeight: number; // 1.0 to 3.0
  letterSpacing: number; // 0 to 10px
  colorScheme: 'default' | 'protanopia' | 'deuteranopia' | 'tritanopia' | 'monochrome' | 'custom';
  customColors: {
    background: string;
    text: string;
    accent: string;
    border: string;
    highlight: string;
  };
  reducedMotion: boolean;
  focusIndicatorStyle: 'default' | 'enhanced' | 'custom';
  cursorEnhancement: boolean;
  textShadow: boolean;
  invertColors: boolean;
  saturation: number; // 0 to 200%
  brightness: number; // 50 to 150%
  contrast: number; // 50 to 200%
  enableDyslexiaFont: boolean;
  enableReadingGuide: boolean;
  readingGuideColor: string;
  readingGuideOpacity: number;
}

export interface ColorBlindnessFilter {
  name: string;
  type: 'protanopia' | 'deuteranopia' | 'tritanopia';
  matrix: number[];
  description: string;
}

export interface AccessibilityProfile {
  id: string;
  name: string;
  description: string;
  config: VisualAccessibilityConfig;
  isDefault: boolean;
  createdAt: Date;
  lastUsed: Date;
}

const DEFAULT_CONFIG: VisualAccessibilityConfig = {
  highContrastMode: 'none',
  fontScaling: 1.0,
  lineHeight: 1.4,
  letterSpacing: 0,
  colorScheme: 'default',
  customColors: {
    background: '#ffffff',
    text: '#000000',
    accent: '#0066cc',
    border: '#cccccc',
    highlight: '#ffff00',
  },
  reducedMotion: false,
  focusIndicatorStyle: 'default',
  cursorEnhancement: false,
  textShadow: false,
  invertColors: false,
  saturation: 100,
  brightness: 100,
  contrast: 100,
  enableDyslexiaFont: false,
  enableReadingGuide: false,
  readingGuideColor: '#ff0000',
  readingGuideOpacity: 0.3,
};

const COLOR_BLINDNESS_FILTERS: ColorBlindnessFilter[] = [
  {
    name: 'Protanopia',
    type: 'protanopia',
    matrix: [0.567, 0.433, 0, 0.558, 0.442, 0, 0, 0.242, 0.758],
    description: 'Red-blind (missing L-cones)',
  },
  {
    name: 'Deuteranopia',
    type: 'deuteranopia',
    matrix: [0.625, 0.375, 0, 0.7, 0.3, 0, 0, 0.3, 0.7],
    description: 'Green-blind (missing M-cones)',
  },
  {
    name: 'Tritanopia',
    type: 'tritanopia',
    matrix: [0.95, 0.05, 0, 0, 0.433, 0.567, 0, 0.475, 0.525],
    description: 'Blue-blind (missing S-cones)',
  },
];

const PREDEFINED_PROFILES: Omit<AccessibilityProfile, 'id' | 'createdAt' | 'lastUsed'>[] = [
  {
    name: 'High Contrast Light',
    description: 'High contrast mode with light background',
    isDefault: false,
    config: {
      ...DEFAULT_CONFIG,
      highContrastMode: 'light',
      customColors: {
        background: '#ffffff',
        text: '#000000',
        accent: '#0000ff',
        border: '#000000',
        highlight: '#ffff00',
      },
      contrast: 150,
    },
  },
  {
    name: 'High Contrast Dark',
    description: 'High contrast mode with dark background',
    isDefault: false,
    config: {
      ...DEFAULT_CONFIG,
      highContrastMode: 'dark',
      customColors: {
        background: '#000000',
        text: '#ffffff',
        accent: '#00ffff',
        border: '#ffffff',
        highlight: '#ffff00',
      },
      contrast: 150,
    },
  },
  {
    name: 'Dyslexia Friendly',
    description: 'Optimized for users with dyslexia',
    isDefault: false,
    config: {
      ...DEFAULT_CONFIG,
      enableDyslexiaFont: true,
      fontScaling: 1.2,
      lineHeight: 1.8,
      letterSpacing: 2,
      customColors: {
        background: '#fdf6e3',
        text: '#2e3440',
        accent: '#5e81ac',
        border: '#d8dee9',
        highlight: '#ebcb8b',
      },
    },
  },
  {
    name: 'Low Vision',
    description: 'Enhanced visibility for low vision users',
    isDefault: false,
    config: {
      ...DEFAULT_CONFIG,
      fontScaling: 1.5,
      lineHeight: 2.0,
      letterSpacing: 3,
      textShadow: true,
      cursorEnhancement: true,
      focusIndicatorStyle: 'enhanced',
      brightness: 120,
      contrast: 130,
    },
  },
];

export class VisualAccessibilityManager {
  private config: VisualAccessibilityConfig;
  private profiles: Map<string, AccessibilityProfile> = new Map();
  private currentProfile: AccessibilityProfile | null = null;
  private styleElement: HTMLStyleElement | null = null;
  private readingGuide: HTMLElement | null = null;
  private eventListeners: Map<string, Set<(data: any) => void>> = new Map();

  constructor(config: Partial<VisualAccessibilityConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    
    this.initializePredefinedProfiles();
    this.loadUserProfiles();
    this.createStyleElement();
    this.setupEventListeners();
    this.applyConfiguration();
  }

  private initializePredefinedProfiles(): void {
    PREDEFINED_PROFILES.forEach(profile => {
      const fullProfile: AccessibilityProfile = {
        ...profile,
        id: `predefined-${profile.name.toLowerCase().replace(/\s+/g, '-')}`,
        createdAt: new Date(),
        lastUsed: new Date(),
      };
      this.profiles.set(fullProfile.id, fullProfile);
    });
  }

  private loadUserProfiles(): void {
    if (typeof localStorage === 'undefined') return;

    try {
      const saved = localStorage.getItem('visual-accessibility-profiles');
      if (saved) {
        const profiles = JSON.parse(saved);
        if (Array.isArray(profiles)) {
          profiles.forEach((profile: any) => {
            if (profile && typeof profile === 'object') {
              this.profiles.set(profile.id, {
                ...profile,
                createdAt: new Date(profile.createdAt || Date.now()),
                lastUsed: new Date(profile.lastUsed || Date.now()),
              });
            }
          });
        }
      }

      const currentProfileId = localStorage.getItem('visual-accessibility-current-profile');
      if (currentProfileId && this.profiles.has(currentProfileId)) {
        this.currentProfile = this.profiles.get(currentProfileId)!;
        this.config = { ...this.currentProfile.config };
      }
    } catch (error) {
      console.warn('Failed to load accessibility profiles:', error);
    }
  }

  private saveUserProfiles(): void {
    if (typeof localStorage === 'undefined') return;

    try {
      const userProfiles = Array.from(this.profiles.values()).filter(p => !p.id.startsWith('predefined-'));
      localStorage.setItem('visual-accessibility-profiles', JSON.stringify(userProfiles));

      if (this.currentProfile) {
        localStorage.setItem('visual-accessibility-current-profile', this.currentProfile.id);
      }
    } catch (error) {
      console.warn('Failed to save accessibility profiles:', error);
    }
  }

  private createStyleElement(): void {
    if (typeof document === 'undefined') return;

    this.styleElement = document.createElement('style');
    this.styleElement.id = 'visual-accessibility-styles';
    document.head.appendChild(this.styleElement);
  }

  private setupEventListeners(): void {
    // Listen for system preference changes
    if (window.matchMedia) {
      const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
      prefersReducedMotion.addEventListener('change', (e) => {
        if (e.matches) {
          this.updateConfig({ reducedMotion: true });
        }
      });

      const prefersHighContrast = window.matchMedia('(prefers-contrast: high)');
      prefersHighContrast.addEventListener('change', (e) => {
        if (e.matches && this.config.highContrastMode === 'none') {
          this.updateConfig({ highContrastMode: 'light' });
        }
      });
    }

    // Setup reading guide
    if (this.config.enableReadingGuide) {
      this.setupReadingGuide();
    }
  }

  private setupReadingGuide(): void {
    if (this.readingGuide) return;

    this.readingGuide = document.createElement('div');
    this.readingGuide.id = 'reading-guide';
    this.readingGuide.style.cssText = `
      position: fixed;
      left: 0;
      right: 0;
      height: 3px;
      background-color: ${this.config.readingGuideColor};
      opacity: ${this.config.readingGuideOpacity};
      pointer-events: none;
      z-index: 9999;
      transition: top 0.1s ease;
      display: none;
    `;
    document.body.appendChild(this.readingGuide);

    // Track mouse movement to position reading guide
    document.addEventListener('mousemove', (e) => {
      if (this.readingGuide && this.config.enableReadingGuide) {
        this.readingGuide.style.top = `${e.clientY}px`;
        this.readingGuide.style.display = 'block';
      }
    });

    document.addEventListener('mouseleave', () => {
      if (this.readingGuide) {
        this.readingGuide.style.display = 'none';
      }
    });
  }

  public updateConfig(updates: Partial<VisualAccessibilityConfig>): void {
    this.config = { ...this.config, ...updates };
    this.applyConfiguration();
    this.emit('config-updated', { config: this.config });
  }

  private applyConfiguration(): void {
    if (!this.styleElement) return;

    const styles = this.generateCSS();
    this.styleElement.textContent = styles;

    // Apply reading guide changes
    if (this.config.enableReadingGuide && !this.readingGuide) {
      this.setupReadingGuide();
    } else if (!this.config.enableReadingGuide && this.readingGuide) {
      this.readingGuide.remove();
      this.readingGuide = null;
    } else if (this.readingGuide) {
      this.readingGuide.style.backgroundColor = this.config.readingGuideColor;
      this.readingGuide.style.opacity = String(this.config.readingGuideOpacity);
    }

    // Apply document-level attributes
    document.documentElement.setAttribute('data-accessibility-mode', 'enabled');
    document.documentElement.setAttribute('data-high-contrast', this.config.highContrastMode);
    document.documentElement.setAttribute('data-color-scheme', this.config.colorScheme);
    
    if (this.config.reducedMotion) {
      document.documentElement.setAttribute('data-reduced-motion', 'true');
    } else {
      document.documentElement.removeAttribute('data-reduced-motion');
    }
  }

  private generateCSS(): string {
    const { config } = this;
    let css = '';

    // Base accessibility styles
    css += `
      [data-accessibility-mode="enabled"] {
        --font-scale: ${config.fontScaling};
        --line-height: ${config.lineHeight};
        --letter-spacing: ${config.letterSpacing}px;
        --bg-color: ${config.customColors.background};
        --text-color: ${config.customColors.text};
        --accent-color: ${config.customColors.accent};
        --border-color: ${config.customColors.border};
        --highlight-color: ${config.customColors.highlight};
      }
    `;

    // Font scaling
    css += `
      [data-accessibility-mode="enabled"] * {
        font-size: calc(1em * var(--font-scale)) !important;
        line-height: var(--line-height) !important;
        letter-spacing: var(--letter-spacing) !important;
      }
    `;

    // High contrast modes
    if (config.highContrastMode !== 'none') {
      css += `
        [data-high-contrast="${config.highContrastMode}"] {
          background-color: var(--bg-color) !important;
          color: var(--text-color) !important;
        }
        
        [data-high-contrast="${config.highContrastMode}"] * {
          background-color: var(--bg-color) !important;
          color: var(--text-color) !important;
          border-color: var(--border-color) !important;
        }
        
        [data-high-contrast="${config.highContrastMode}"] a,
        [data-high-contrast="${config.highContrastMode}"] button {
          color: var(--accent-color) !important;
        }
        
        [data-high-contrast="${config.highContrastMode}"] ::selection {
          background-color: var(--highlight-color) !important;
          color: var(--text-color) !important;
        }
      `;
    }

    // Color blindness filters
    if (config.colorScheme !== 'default' && config.colorScheme !== 'custom') {
      const filter = COLOR_BLINDNESS_FILTERS.find(f => f.type === config.colorScheme);
      if (filter) {
        const matrix = filter.matrix;
        css += `
          [data-color-scheme="${config.colorScheme}"] {
            filter: url('#colorblind-filter-${config.colorScheme}');
          }
        `;
        
        // Add SVG filter to document
        this.addColorBlindnessFilter(config.colorScheme, matrix);
      }
    }

    // Monochrome mode
    if (config.colorScheme === 'monochrome') {
      css += `
        [data-color-scheme="monochrome"] {
          filter: grayscale(100%);
        }
      `;
    }

    // Visual enhancements
    if (config.textShadow) {
      css += `
        [data-accessibility-mode="enabled"] {
          text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5) !important;
        }
      `;
    }

    if (config.invertColors) {
      css += `
        [data-accessibility-mode="enabled"] {
          filter: invert(1) hue-rotate(180deg);
        }
      `;
    }

    // Image and media filters
    css += `
      [data-accessibility-mode="enabled"] img,
      [data-accessibility-mode="enabled"] video {
        filter: 
          brightness(${config.brightness}%) 
          contrast(${config.contrast}%) 
          saturate(${config.saturation}%);
      }
    `;

    // Enhanced focus indicators
    if (config.focusIndicatorStyle === 'enhanced') {
      css += `
        [data-accessibility-mode="enabled"] *:focus {
          outline: 3px solid var(--accent-color) !important;
          outline-offset: 2px !important;
          box-shadow: 0 0 0 5px rgba(0, 102, 204, 0.3) !important;
        }
      `;
    }

    // Cursor enhancement
    if (config.cursorEnhancement) {
      css += `
        [data-accessibility-mode="enabled"] {
          cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32"><circle cx="16" cy="16" r="10" fill="red" stroke="white" stroke-width="2"/></svg>') 16 16, auto !important;
        }
      `;
    }

    // Dyslexia font
    if (config.enableDyslexiaFont) {
      css += `
        [data-accessibility-mode="enabled"] * {
          font-family: 'OpenDyslexic', 'Comic Sans MS', cursive !important;
        }
      `;
    }

    // Reduced motion
    if (config.reducedMotion) {
      css += `
        [data-reduced-motion="true"] *,
        [data-reduced-motion="true"] *::before,
        [data-reduced-motion="true"] *::after {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
          scroll-behavior: auto !important;
        }
      `;
    }

    return css;
  }

  private addColorBlindnessFilter(type: string, matrix: number[]): void {
    let svg = document.getElementById('accessibility-filters') as SVGElement;
    if (!svg) {
      svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
      svg.id = 'accessibility-filters';
      svg.style.cssText = 'position: absolute; width: 0; height: 0; pointer-events: none;';
      document.body.appendChild(svg);
    }

    const defs = svg.querySelector('defs') || document.createElementNS('http://www.w3.org/2000/svg', 'defs');
    if (!svg.querySelector('defs')) {
      svg.appendChild(defs);
    }

    const filter = document.createElementNS('http://www.w3.org/2000/svg', 'filter');
    filter.id = `colorblind-filter-${type}`;

    const colorMatrix = document.createElementNS('http://www.w3.org/2000/svg', 'feColorMatrix');
    colorMatrix.setAttribute('type', 'matrix');
    colorMatrix.setAttribute('values', matrix.join(' '));

    filter.appendChild(colorMatrix);
    defs.appendChild(filter);
  }

  public createProfile(name: string, description: string, config?: Partial<VisualAccessibilityConfig>): string {
    const profileId = `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const profile: AccessibilityProfile = {
      id: profileId,
      name,
      description,
      config: config ? { ...this.config, ...config } : { ...this.config },
      isDefault: false,
      createdAt: new Date(),
      lastUsed: new Date(),
    };

    this.profiles.set(profileId, profile);
    this.saveUserProfiles();
    this.emit('profile-created', { profile });
    
    return profileId;
  }

  public loadProfile(profileId: string): boolean {
    const profile = this.profiles.get(profileId);
    if (!profile) return false;

    this.currentProfile = profile;
    this.config = { ...profile.config };
    profile.lastUsed = new Date();
    
    this.applyConfiguration();
    this.saveUserProfiles();
    this.emit('profile-loaded', { profile });
    
    return true;
  }

  public deleteProfile(profileId: string): boolean {
    if (profileId.startsWith('predefined-')) return false;
    
    const deleted = this.profiles.delete(profileId);
    if (deleted) {
      if (this.currentProfile?.id === profileId) {
        this.currentProfile = null;
        this.config = { ...DEFAULT_CONFIG };
        this.applyConfiguration();
      }
      this.saveUserProfiles();
      this.emit('profile-deleted', { profileId });
    }
    
    return deleted;
  }

  public getProfiles(): AccessibilityProfile[] {
    return Array.from(this.profiles.values()).sort((a, b) => 
      b.lastUsed.getTime() - a.lastUsed.getTime()
    );
  }

  public getCurrentProfile(): AccessibilityProfile | null {
    return this.currentProfile;
  }

  public getConfig(): VisualAccessibilityConfig {
    return { ...this.config };
  }

  public getColorBlindnessFilters(): ColorBlindnessFilter[] {
    return [...COLOR_BLINDNESS_FILTERS];
  }

  public resetToDefaults(): void {
    this.config = { ...DEFAULT_CONFIG };
    this.currentProfile = null;
    this.applyConfiguration();
    this.emit('reset-to-defaults', {});
  }

  public addEventListener(type: string, listener: (data: any) => void): void {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, new Set());
    }
    this.eventListeners.get(type)!.add(listener);
  }

  public removeEventListener(type: string, listener: (data: any) => void): void {
    this.eventListeners.get(type)?.delete(listener);
  }

  private emit(type: string, data: any): void {
    this.eventListeners.get(type)?.forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        console.error('Visual accessibility event listener error:', error);
      }
    });
  }

  public destroy(): void {
    if (this.styleElement) {
      this.styleElement.remove();
    }
    
    if (this.readingGuide) {
      this.readingGuide.remove();
    }
    
    const svg = document.getElementById('accessibility-filters');
    if (svg) {
      svg.remove();
    }
    
    this.eventListeners.clear();
  }
}
