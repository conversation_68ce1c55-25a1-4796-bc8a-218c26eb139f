/**
 * Screen Reader Support & Accessibility Manager
 * Comprehensive accessibility features for PDF viewing with screen reader support
 */

export interface AccessibilityConfig {
  enableScreenReader: boolean;
  enableKeyboardNavigation: boolean;
  enableHighContrast: boolean;
  enableLargeText: boolean;
  enableReducedMotion: boolean;
  announcePageChanges: boolean;
  announceZoomChanges: boolean;
  announceLoadingStates: boolean;
  enableStructuralNavigation: boolean;
  enableTextToSpeech: boolean;
  speechRate: number; // 0.1 to 10
  speechPitch: number; // 0 to 2
  speechVolume: number; // 0 to 1
}

export interface StructuralElement {
  type: 'heading' | 'paragraph' | 'list' | 'table' | 'image' | 'link' | 'form';
  level?: number; // For headings
  text: string;
  bounds: { x: number; y: number; width: number; height: number };
  pageNumber: number;
  id: string;
}

export interface AccessibilityAnnouncement {
  message: string;
  priority: 'polite' | 'assertive' | 'off';
  interrupt: boolean;
}

const DEFAULT_CONFIG: AccessibilityConfig = {
  enableScreenReader: true,
  enableKeyboardNavigation: true,
  enableHighContrast: false,
  enableLargeText: false,
  enableReducedMotion: false,
  announcePageChanges: true,
  announceZoomChanges: true,
  announceLoadingStates: true,
  enableStructuralNavigation: true,
  enableTextToSpeech: false,
  speechRate: 1,
  speechPitch: 1,
  speechVolume: 0.8,
};

export class AccessibilityManager {
  private config: AccessibilityConfig;
  private liveRegion: HTMLElement | null = null;
  private structuralElements: StructuralElement[] = [];
  private currentElementIndex = -1;
  private speechSynthesis: SpeechSynthesis | null = null;
  private currentUtterance: SpeechSynthesisUtterance | null = null;
  private keyboardHandlers: Map<string, (event: KeyboardEvent) => void> = new Map();

  constructor(config: Partial<AccessibilityConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.initialize();
  }

  private initialize(): void {
    this.createLiveRegion();
    this.setupSpeechSynthesis();
    this.setupKeyboardNavigation();
    this.detectUserPreferences();
    this.setupMutationObserver();
  }

  private createLiveRegion(): void {
    // Create ARIA live region for announcements
    this.liveRegion = document.createElement('div');
    this.liveRegion.setAttribute('aria-live', 'polite');
    this.liveRegion.setAttribute('aria-atomic', 'true');
    this.liveRegion.setAttribute('aria-relevant', 'additions text');
    this.liveRegion.style.position = 'absolute';
    this.liveRegion.style.left = '-10000px';
    this.liveRegion.style.width = '1px';
    this.liveRegion.style.height = '1px';
    this.liveRegion.style.overflow = 'hidden';
    document.body.appendChild(this.liveRegion);
  }

  private setupSpeechSynthesis(): void {
    if ('speechSynthesis' in window) {
      this.speechSynthesis = window.speechSynthesis;
    }
  }

  private setupKeyboardNavigation(): void {
    if (!this.config.enableKeyboardNavigation) return;

    // Define keyboard shortcuts
    this.keyboardHandlers.set('ArrowRight', () => this.navigateToNextElement());
    this.keyboardHandlers.set('ArrowLeft', () => this.navigateToPreviousElement());
    this.keyboardHandlers.set('ArrowDown', () => this.navigateToNextElement());
    this.keyboardHandlers.set('ArrowUp', () => this.navigateToPreviousElement());
    this.keyboardHandlers.set('Home', () => this.navigateToFirstElement());
    this.keyboardHandlers.set('End', () => this.navigateToLastElement());
    this.keyboardHandlers.set('h', () => this.navigateToNextHeading());
    this.keyboardHandlers.set('H', () => this.navigateToPreviousHeading());
    this.keyboardHandlers.set('p', () => this.navigateToNextParagraph());
    this.keyboardHandlers.set('P', () => this.navigateToPreviousParagraph());
    this.keyboardHandlers.set('l', () => this.navigateToNextList());
    this.keyboardHandlers.set('L', () => this.navigateToPreviousList());
    this.keyboardHandlers.set('t', () => this.navigateToNextTable());
    this.keyboardHandlers.set('T', () => this.navigateToPreviousTable());
    this.keyboardHandlers.set('g', () => this.navigateToNextImage());
    this.keyboardHandlers.set('G', () => this.navigateToPreviousImage());
    this.keyboardHandlers.set('Space', () => this.toggleSpeech());
    this.keyboardHandlers.set('Escape', () => this.stopSpeech());

    // Add global keyboard event listener
    document.addEventListener('keydown', this.handleKeyboardEvent.bind(this));
  }

  private handleKeyboardEvent(event: KeyboardEvent): void {
    // Only handle if focus is on PDF viewer
    const target = event.target as HTMLElement;
    if (!target.closest('[data-pdf-viewer]')) return;

    const key = event.key;
    const handler = this.keyboardHandlers.get(key);
    
    if (handler) {
      event.preventDefault();
      handler(event);
    }
  }

  private detectUserPreferences(): void {
    // Detect system preferences
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      this.config.enableReducedMotion = true;
    }

    if (window.matchMedia('(prefers-contrast: high)').matches) {
      this.config.enableHighContrast = true;
    }

    // Listen for preference changes
    window.matchMedia('(prefers-reduced-motion: reduce)').addEventListener('change', (e) => {
      this.config.enableReducedMotion = e.matches;
      this.announce('Motion preferences updated', 'polite');
    });

    window.matchMedia('(prefers-contrast: high)').addEventListener('change', (e) => {
      this.config.enableHighContrast = e.matches;
      this.announce('Contrast preferences updated', 'polite');
    });
  }

  private setupMutationObserver(): void {
    // Monitor DOM changes for accessibility updates
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          this.updateAccessibilityTree();
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });
  }

  public announce(message: string, priority: AccessibilityAnnouncement['priority'] = 'polite', interrupt = false): void {
    if (!this.config.enableScreenReader || !this.liveRegion) return;

    // Stop current speech if interrupting
    if (interrupt && this.currentUtterance) {
      this.stopSpeech();
    }

    // Update live region
    this.liveRegion.setAttribute('aria-live', priority);
    this.liveRegion.textContent = message;

    // Use speech synthesis if enabled
    if (this.config.enableTextToSpeech && this.speechSynthesis) {
      this.speak(message, interrupt);
    }
  }

  private speak(text: string, interrupt = false): void {
    if (!this.speechSynthesis) return;

    if (interrupt) {
      this.speechSynthesis.cancel();
    }

    this.currentUtterance = new SpeechSynthesisUtterance(text);
    this.currentUtterance.rate = this.config.speechRate;
    this.currentUtterance.pitch = this.config.speechPitch;
    this.currentUtterance.volume = this.config.speechVolume;

    this.currentUtterance.onend = () => {
      this.currentUtterance = null;
    };

    this.speechSynthesis.speak(this.currentUtterance);
  }

  private stopSpeech(): void {
    if (this.speechSynthesis) {
      this.speechSynthesis.cancel();
      this.currentUtterance = null;
    }
  }

  private toggleSpeech(): void {
    if (this.currentUtterance) {
      this.stopSpeech();
      this.announce('Speech stopped', 'assertive');
    } else if (this.currentElementIndex >= 0) {
      const element = this.structuralElements[this.currentElementIndex];
      this.speak(element.text);
    }
  }

  public updateStructuralElements(elements: StructuralElement[]): void {
    this.structuralElements = elements;
    this.updateAccessibilityTree();
  }

  private updateAccessibilityTree(): void {
    // Update ARIA labels and descriptions for structural elements
    this.structuralElements.forEach((element, index) => {
      const domElement = document.querySelector(`[data-element-id="${element.id}"]`);
      if (domElement) {
        domElement.setAttribute('role', this.getAriaRole(element.type));
        domElement.setAttribute('aria-label', this.getAriaLabel(element));
        domElement.setAttribute('tabindex', '0');
        
        if (element.type === 'heading' && element.level) {
          domElement.setAttribute('aria-level', element.level.toString());
        }
      }
    });
  }

  private getAriaRole(type: StructuralElement['type']): string {
    switch (type) {
      case 'heading': return 'heading';
      case 'paragraph': return 'paragraph';
      case 'list': return 'list';
      case 'table': return 'table';
      case 'image': return 'img';
      case 'link': return 'link';
      case 'form': return 'form';
      default: return 'text';
    }
  }

  private getAriaLabel(element: StructuralElement): string {
    const typeLabel = element.type === 'heading' && element.level 
      ? `Heading level ${element.level}` 
      : element.type;
    
    return `${typeLabel}: ${element.text.substring(0, 100)}${element.text.length > 100 ? '...' : ''}`;
  }

  // Navigation methods
  private navigateToNextElement(): void {
    if (this.currentElementIndex < this.structuralElements.length - 1) {
      this.currentElementIndex++;
      this.focusCurrentElement();
    }
  }

  private navigateToPreviousElement(): void {
    if (this.currentElementIndex > 0) {
      this.currentElementIndex--;
      this.focusCurrentElement();
    }
  }

  private navigateToFirstElement(): void {
    if (this.structuralElements.length > 0) {
      this.currentElementIndex = 0;
      this.focusCurrentElement();
    }
  }

  private navigateToLastElement(): void {
    if (this.structuralElements.length > 0) {
      this.currentElementIndex = this.structuralElements.length - 1;
      this.focusCurrentElement();
    }
  }

  private navigateToNextHeading(): void {
    this.navigateToNextElementOfType('heading');
  }

  private navigateToPreviousHeading(): void {
    this.navigateToPreviousElementOfType('heading');
  }

  private navigateToNextParagraph(): void {
    this.navigateToNextElementOfType('paragraph');
  }

  private navigateToPreviousParagraph(): void {
    this.navigateToPreviousElementOfType('paragraph');
  }

  private navigateToNextList(): void {
    this.navigateToNextElementOfType('list');
  }

  private navigateToPreviousList(): void {
    this.navigateToPreviousElementOfType('list');
  }

  private navigateToNextTable(): void {
    this.navigateToNextElementOfType('table');
  }

  private navigateToPreviousTable(): void {
    this.navigateToPreviousElementOfType('table');
  }

  private navigateToNextImage(): void {
    this.navigateToNextElementOfType('image');
  }

  private navigateToPreviousImage(): void {
    this.navigateToPreviousElementOfType('image');
  }

  private navigateToNextElementOfType(type: StructuralElement['type']): void {
    for (let i = this.currentElementIndex + 1; i < this.structuralElements.length; i++) {
      if (this.structuralElements[i].type === type) {
        this.currentElementIndex = i;
        this.focusCurrentElement();
        return;
      }
    }
    this.announce(`No more ${type}s found`, 'polite');
  }

  private navigateToPreviousElementOfType(type: StructuralElement['type']): void {
    for (let i = this.currentElementIndex - 1; i >= 0; i--) {
      if (this.structuralElements[i].type === type) {
        this.currentElementIndex = i;
        this.focusCurrentElement();
        return;
      }
    }
    this.announce(`No previous ${type}s found`, 'polite');
  }

  private focusCurrentElement(): void {
    if (this.currentElementIndex < 0 || this.currentElementIndex >= this.structuralElements.length) {
      return;
    }

    const element = this.structuralElements[this.currentElementIndex];
    const domElement = document.querySelector(`[data-element-id="${element.id}"]`) as HTMLElement;
    
    if (domElement) {
      domElement.focus();
      domElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      
      // Announce the element
      this.announce(this.getAriaLabel(element), 'assertive');
    }
  }

  // Public API methods
  public announcePageChange(pageNumber: number, totalPages: number): void {
    if (this.config.announcePageChanges) {
      this.announce(`Page ${pageNumber} of ${totalPages}`, 'polite');
    }
  }

  public announceZoomChange(zoomLevel: number): void {
    if (this.config.announceZoomChanges) {
      this.announce(`Zoom level ${Math.round(zoomLevel * 100)}%`, 'polite');
    }
  }

  public announceLoadingState(state: 'loading' | 'loaded' | 'error', details?: string): void {
    if (this.config.announceLoadingStates) {
      let message = '';
      switch (state) {
        case 'loading':
          message = 'Loading document...';
          break;
        case 'loaded':
          message = 'Document loaded successfully';
          break;
        case 'error':
          message = `Error loading document${details ? ': ' + details : ''}`;
          break;
      }
      this.announce(message, state === 'error' ? 'assertive' : 'polite');
    }
  }

  public updateConfig(newConfig: Partial<AccessibilityConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Update speech synthesis settings
    if (this.currentUtterance) {
      this.currentUtterance.rate = this.config.speechRate;
      this.currentUtterance.pitch = this.config.speechPitch;
      this.currentUtterance.volume = this.config.speechVolume;
    }
  }

  public getConfig(): AccessibilityConfig {
    return { ...this.config };
  }

  public getCurrentElement(): StructuralElement | null {
    if (this.currentElementIndex >= 0 && this.currentElementIndex < this.structuralElements.length) {
      return this.structuralElements[this.currentElementIndex];
    }
    return null;
  }

  public getStructuralElements(): StructuralElement[] {
    return [...this.structuralElements];
  }

  public destroy(): void {
    // Remove event listeners
    document.removeEventListener('keydown', this.handleKeyboardEvent.bind(this));
    
    // Stop speech
    this.stopSpeech();
    
    // Remove live region
    if (this.liveRegion && this.liveRegion.parentNode) {
      this.liveRegion.parentNode.removeChild(this.liveRegion);
    }
    
    // Clear data
    this.structuralElements = [];
    this.keyboardHandlers.clear();
  }
}
