/**
 * Reading Assistance Tools
 * Advanced reading guides, focus indicators, and cognitive assistance features
 */

export interface ReadingAssistanceConfig {
  enableReadingGuide: boolean;
  readingGuideType: 'line' | 'ruler' | 'window' | 'spotlight';
  readingGuideColor: string;
  readingGuideOpacity: number;
  readingGuideWidth: number;
  enableFocusIndicator: boolean;
  focusIndicatorStyle: 'outline' | 'highlight' | 'shadow' | 'glow';
  focusIndicatorColor: string;
  enableWordHighlight: boolean;
  wordHighlightColor: string;
  enableLineSpacing: boolean;
  lineSpacingMultiplier: number;
  enableReadingMask: boolean;
  maskOpacity: number;
  maskColor: string;
  enableBionic: boolean;
  bionicIntensity: number;
  enableSyllableBreaks: boolean;
  enableReadingSpeed: boolean;
  targetReadingSpeed: number; // words per minute
  enablePauseReminders: boolean;
  pauseReminderInterval: number; // minutes
  enableComprehensionAids: boolean;
  enableTextToSpeech: boolean;
  speechRate: number;
  speechPitch: number;
  speechVoice: string;
}

export interface ReadingSession {
  id: string;
  startTime: Date;
  endTime?: Date;
  wordsRead: number;
  timeSpent: number;
  averageSpeed: number;
  pauseCount: number;
  comprehensionScore?: number;
  focusScore: number;
  assistanceUsed: string[];
}

export interface ReadingGuide {
  element: HTMLElement;
  type: ReadingAssistanceConfig['readingGuideType'];
  isActive: boolean;
  position: { x: number; y: number };
  dimensions: { width: number; height: number };
}

export interface FocusIndicator {
  element: HTMLElement;
  target: HTMLElement;
  style: ReadingAssistanceConfig['focusIndicatorStyle'];
  isActive: boolean;
}

export interface ComprehensionAid {
  type: 'summary' | 'definition' | 'context' | 'question';
  content: string;
  position: { x: number; y: number };
  element: HTMLElement;
}

const DEFAULT_CONFIG: ReadingAssistanceConfig = {
  enableReadingGuide: false,
  readingGuideType: 'line',
  readingGuideColor: '#ff0000',
  readingGuideOpacity: 0.3,
  readingGuideWidth: 3,
  enableFocusIndicator: true,
  focusIndicatorStyle: 'outline',
  focusIndicatorColor: '#0066cc',
  enableWordHighlight: false,
  wordHighlightColor: '#ffff00',
  enableLineSpacing: false,
  lineSpacingMultiplier: 1.5,
  enableReadingMask: false,
  maskOpacity: 0.8,
  maskColor: '#000000',
  enableBionic: false,
  bionicIntensity: 0.5,
  enableSyllableBreaks: false,
  enableReadingSpeed: false,
  targetReadingSpeed: 200,
  enablePauseReminders: false,
  pauseReminderInterval: 20,
  enableComprehensionAids: false,
  enableTextToSpeech: false,
  speechRate: 1.0,
  speechPitch: 1.0,
  speechVoice: '',
};

export class ReadingAssistanceManager {
  private config: ReadingAssistanceConfig;
  private readingGuide: ReadingGuide | null = null;
  private focusIndicator: FocusIndicator | null = null;
  private readingMask: HTMLElement | null = null;
  private currentSession: ReadingSession | null = null;
  private speechSynthesis: SpeechSynthesis | null = null;
  private speechUtterance: SpeechSynthesisUtterance | null = null;
  private comprehensionAids: Map<string, ComprehensionAid> = new Map();
  private pauseReminderTimer: number | null = null;
  private wordCount = 0;
  private lastWordTime = 0;
  private eventListeners: Map<string, Set<(data: any) => void>> = new Map();

  constructor(config: Partial<ReadingAssistanceConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    
    this.initializeSpeechSynthesis();
    this.setupEventListeners();
    this.applyConfiguration();
  }

  private initializeSpeechSynthesis(): void {
    if (typeof window !== 'undefined' && 'speechSynthesis' in window) {
      this.speechSynthesis = window.speechSynthesis;
    }
  }

  private setupEventListeners(): void {
    if (typeof document === 'undefined') return;

    document.addEventListener('mousemove', this.handleMouseMove.bind(this));
    document.addEventListener('click', this.handleClick.bind(this));
    document.addEventListener('keydown', this.handleKeyDown.bind(this));
    document.addEventListener('scroll', this.handleScroll.bind(this));
    document.addEventListener('focusin', this.handleFocusIn.bind(this));
    document.addEventListener('focusout', this.handleFocusOut.bind(this));
  }

  private applyConfiguration(): void {
    if (this.config.enableReadingGuide) {
      this.createReadingGuide();
    } else {
      this.removeReadingGuide();
    }

    if (this.config.enableReadingMask) {
      this.createReadingMask();
    } else {
      this.removeReadingMask();
    }

    if (this.config.enablePauseReminders) {
      this.startPauseReminders();
    } else {
      this.stopPauseReminders();
    }

    this.applyTextEnhancements();
  }

  private createReadingGuide(): void {
    if (this.readingGuide || typeof document === 'undefined') return;

    const element = document.createElement('div');
    element.id = 'reading-guide';
    element.style.cssText = `
      position: fixed;
      pointer-events: none;
      z-index: 9999;
      background-color: ${this.config.readingGuideColor};
      opacity: ${this.config.readingGuideOpacity};
      transition: all 0.1s ease;
      display: none;
    `;

    this.updateReadingGuideStyle(element);
    document.body.appendChild(element);

    this.readingGuide = {
      element,
      type: this.config.readingGuideType,
      isActive: false,
      position: { x: 0, y: 0 },
      dimensions: { width: 0, height: 0 },
    };
  }

  private updateReadingGuideStyle(element: HTMLElement): void {
    switch (this.config.readingGuideType) {
      case 'line':
        element.style.width = '100vw';
        element.style.height = `${this.config.readingGuideWidth}px`;
        element.style.left = '0';
        break;
      case 'ruler':
        element.style.width = '100vw';
        element.style.height = '20px';
        element.style.left = '0';
        element.style.border = `2px solid ${this.config.readingGuideColor}`;
        element.style.backgroundColor = 'transparent';
        break;
      case 'window':
        element.style.width = '100vw';
        element.style.height = '60px';
        element.style.left = '0';
        element.style.border = `2px solid ${this.config.readingGuideColor}`;
        element.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
        break;
      case 'spotlight':
        element.style.width = '200px';
        element.style.height = '200px';
        element.style.borderRadius = '50%';
        element.style.boxShadow = `0 0 0 9999px rgba(0, 0, 0, ${this.config.maskOpacity})`;
        element.style.backgroundColor = 'transparent';
        break;
    }
  }

  private removeReadingGuide(): void {
    if (this.readingGuide) {
      this.readingGuide.element.remove();
      this.readingGuide = null;
    }
  }

  private createReadingMask(): void {
    if (this.readingMask) return;

    this.readingMask = document.createElement('div');
    this.readingMask.id = 'reading-mask';
    this.readingMask.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background-color: ${this.config.maskColor};
      opacity: ${this.config.maskOpacity};
      pointer-events: none;
      z-index: 9998;
      clip-path: circle(100px at 50% 50%);
      transition: clip-path 0.1s ease;
    `;

    document.body.appendChild(this.readingMask);
  }

  private removeReadingMask(): void {
    if (this.readingMask) {
      this.readingMask.remove();
      this.readingMask = null;
    }
  }

  private applyTextEnhancements(): void {
    const style = document.getElementById('reading-assistance-styles') || document.createElement('style');
    style.id = 'reading-assistance-styles';
    
    let css = '';

    if (this.config.enableLineSpacing) {
      css += `
        [data-reading-assistance="enabled"] p,
        [data-reading-assistance="enabled"] div {
          line-height: ${this.config.lineSpacingMultiplier} !important;
        }
      `;
    }

    if (this.config.enableWordHighlight) {
      css += `
        .word-highlight {
          background-color: ${this.config.wordHighlightColor} !important;
          transition: background-color 0.2s ease;
        }
      `;
    }

    if (this.config.enableBionic) {
      css += `
        .bionic-text .bionic-bold {
          font-weight: bold;
          opacity: 1;
        }
        .bionic-text .bionic-normal {
          opacity: ${1 - this.config.bionicIntensity};
        }
      `;
    }

    if (this.config.enableSyllableBreaks) {
      css += `
        .syllable-break {
          border-right: 1px solid #ccc;
          padding-right: 1px;
          margin-right: 1px;
        }
      `;
    }

    style.textContent = css;
    if (!style.parentNode) {
      document.head.appendChild(style);
    }

    document.documentElement.setAttribute('data-reading-assistance', 'enabled');
  }

  private handleMouseMove(event: MouseEvent): void {
    if (this.readingGuide && this.config.enableReadingGuide) {
      this.updateReadingGuidePosition(event.clientX, event.clientY);
      this.readingGuide.element.style.display = 'block';
    }

    if (this.readingMask && this.config.enableReadingMask) {
      this.updateReadingMaskPosition(event.clientX, event.clientY);
    }
  }

  private updateReadingGuidePosition(x: number, y: number): void {
    if (!this.readingGuide) return;

    const element = this.readingGuide.element;
    
    switch (this.config.readingGuideType) {
      case 'line':
      case 'ruler':
      case 'window':
        element.style.top = `${y}px`;
        break;
      case 'spotlight':
        element.style.left = `${x - 100}px`;
        element.style.top = `${y - 100}px`;
        break;
    }

    this.readingGuide.position = { x, y };
  }

  private updateReadingMaskPosition(x: number, y: number): void {
    if (!this.readingMask) return;

    this.readingMask.style.clipPath = `circle(100px at ${x}px ${y}px)`;
  }

  private handleClick(event: MouseEvent): void {
    const target = event.target as HTMLElement;
    
    if (this.config.enableWordHighlight) {
      this.highlightWord(target);
    }

    if (this.config.enableTextToSpeech) {
      this.speakText(target.textContent || '');
    }

    this.trackWordReading();
  }

  private handleKeyDown(event: KeyboardEvent): void {
    // Reading assistance shortcuts
    switch (event.key) {
      case 'r':
        if (event.altKey) {
          event.preventDefault();
          this.toggleReadingGuide();
        }
        break;
      case 'm':
        if (event.altKey) {
          event.preventDefault();
          this.toggleReadingMask();
        }
        break;
      case 's':
        if (event.altKey) {
          event.preventDefault();
          this.toggleTextToSpeech();
        }
        break;
    }
  }

  private handleScroll(): void {
    if (this.readingGuide) {
      this.readingGuide.element.style.display = 'none';
    }
  }

  private handleFocusIn(event: FocusEvent): void {
    const target = event.target as HTMLElement;
    
    if (this.config.enableFocusIndicator) {
      this.createFocusIndicator(target);
    }
  }

  private handleFocusOut(): void {
    this.removeFocusIndicator();
  }

  private createFocusIndicator(target: HTMLElement): void {
    this.removeFocusIndicator();

    const indicator = document.createElement('div');
    indicator.id = 'focus-indicator';
    indicator.style.cssText = `
      position: absolute;
      pointer-events: none;
      z-index: 9997;
      transition: all 0.2s ease;
    `;

    this.applyFocusIndicatorStyle(indicator, target);
    document.body.appendChild(indicator);

    this.focusIndicator = {
      element: indicator,
      target,
      style: this.config.focusIndicatorStyle,
      isActive: true,
    };

    this.updateFocusIndicatorPosition();
  }

  private applyFocusIndicatorStyle(indicator: HTMLElement, target: HTMLElement): void {
    const rect = target.getBoundingClientRect();
    
    switch (this.config.focusIndicatorStyle) {
      case 'outline':
        indicator.style.border = `3px solid ${this.config.focusIndicatorColor}`;
        indicator.style.backgroundColor = 'transparent';
        break;
      case 'highlight':
        indicator.style.backgroundColor = this.config.focusIndicatorColor;
        indicator.style.opacity = '0.3';
        break;
      case 'shadow':
        indicator.style.boxShadow = `0 0 10px 5px ${this.config.focusIndicatorColor}`;
        indicator.style.backgroundColor = 'transparent';
        break;
      case 'glow':
        indicator.style.boxShadow = `0 0 20px ${this.config.focusIndicatorColor}`;
        indicator.style.border = `2px solid ${this.config.focusIndicatorColor}`;
        indicator.style.backgroundColor = 'transparent';
        break;
    }

    indicator.style.left = `${rect.left}px`;
    indicator.style.top = `${rect.top}px`;
    indicator.style.width = `${rect.width}px`;
    indicator.style.height = `${rect.height}px`;
  }

  private updateFocusIndicatorPosition(): void {
    if (!this.focusIndicator) return;

    const rect = this.focusIndicator.target.getBoundingClientRect();
    const indicator = this.focusIndicator.element;
    
    indicator.style.left = `${rect.left}px`;
    indicator.style.top = `${rect.top}px`;
    indicator.style.width = `${rect.width}px`;
    indicator.style.height = `${rect.height}px`;
  }

  private removeFocusIndicator(): void {
    if (this.focusIndicator) {
      this.focusIndicator.element.remove();
      this.focusIndicator = null;
    }
  }

  private highlightWord(element: HTMLElement): void {
    // Remove previous highlights
    document.querySelectorAll('.word-highlight').forEach(el => {
      el.classList.remove('word-highlight');
    });

    // Add highlight to current word
    element.classList.add('word-highlight');

    // Remove highlight after delay
    setTimeout(() => {
      element.classList.remove('word-highlight');
    }, 2000);
  }

  private speakText(text: string): void {
    if (!this.speechSynthesis || !text.trim()) return;

    // Stop current speech
    this.speechSynthesis.cancel();

    this.speechUtterance = new SpeechSynthesisUtterance(text);
    this.speechUtterance.rate = this.config.speechRate;
    this.speechUtterance.pitch = this.config.speechPitch;
    
    if (this.config.speechVoice) {
      const voices = this.speechSynthesis.getVoices();
      const voice = voices.find(v => v.name === this.config.speechVoice);
      if (voice) {
        this.speechUtterance.voice = voice;
      }
    }

    this.speechSynthesis.speak(this.speechUtterance);
  }

  private trackWordReading(): void {
    const now = Date.now();
    this.wordCount++;
    
    if (this.lastWordTime > 0) {
      const timeDiff = now - this.lastWordTime;
      const wordsPerMinute = (60000 / timeDiff);
      
      if (this.config.enableReadingSpeed) {
        this.emit('reading-speed-update', { 
          currentSpeed: wordsPerMinute,
          targetSpeed: this.config.targetReadingSpeed,
          wordCount: this.wordCount 
        });
      }
    }
    
    this.lastWordTime = now;
  }

  private startPauseReminders(): void {
    if (this.pauseReminderTimer) return;

    this.pauseReminderTimer = window.setInterval(() => {
      this.showPauseReminder();
    }, this.config.pauseReminderInterval * 60000);
  }

  private stopPauseReminders(): void {
    if (this.pauseReminderTimer) {
      clearInterval(this.pauseReminderTimer);
      this.pauseReminderTimer = null;
    }
  }

  private showPauseReminder(): void {
    const reminder = document.createElement('div');
    reminder.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #333;
      color: white;
      padding: 15px;
      border-radius: 8px;
      z-index: 10000;
      animation: slideIn 0.3s ease;
    `;
    reminder.textContent = 'Time for a reading break! Rest your eyes for a moment.';
    
    document.body.appendChild(reminder);
    
    setTimeout(() => {
      reminder.remove();
    }, 5000);

    this.emit('pause-reminder', { timestamp: new Date() });
  }

  public toggleReadingGuide(): void {
    this.updateConfig({ enableReadingGuide: !this.config.enableReadingGuide });
  }

  public toggleReadingMask(): void {
    this.updateConfig({ enableReadingMask: !this.config.enableReadingMask });
  }

  public toggleTextToSpeech(): void {
    if (this.config.enableTextToSpeech && this.speechSynthesis) {
      this.speechSynthesis.cancel();
    }
    this.updateConfig({ enableTextToSpeech: !this.config.enableTextToSpeech });
  }

  public updateConfig(updates: Partial<ReadingAssistanceConfig>): void {
    this.config = { ...this.config, ...updates };
    this.applyConfiguration();
    this.emit('config-updated', { config: this.config });
  }

  public getConfig(): ReadingAssistanceConfig {
    return { ...this.config };
  }

  public startReadingSession(): string {
    const sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    this.currentSession = {
      id: sessionId,
      startTime: new Date(),
      wordsRead: 0,
      timeSpent: 0,
      averageSpeed: 0,
      pauseCount: 0,
      focusScore: 1.0,
      assistanceUsed: [],
    };

    this.emit('session-started', { session: this.currentSession });
    return sessionId;
  }

  public endReadingSession(): ReadingSession | null {
    if (!this.currentSession) return null;

    this.currentSession.endTime = new Date();
    this.currentSession.timeSpent = 
      this.currentSession.endTime.getTime() - this.currentSession.startTime.getTime();
    this.currentSession.wordsRead = this.wordCount;
    
    if (this.currentSession.timeSpent > 0) {
      this.currentSession.averageSpeed = 
        (this.currentSession.wordsRead / (this.currentSession.timeSpent / 60000));
    }

    const session = this.currentSession;
    this.currentSession = null;
    this.wordCount = 0;
    this.lastWordTime = 0;

    this.emit('session-ended', { session });
    return session;
  }

  public getAvailableVoices(): SpeechSynthesisVoice[] {
    return this.speechSynthesis ? this.speechSynthesis.getVoices() : [];
  }

  public addEventListener(type: string, listener: (data: any) => void): void {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, new Set());
    }
    this.eventListeners.get(type)!.add(listener);
  }

  public removeEventListener(type: string, listener: (data: any) => void): void {
    this.eventListeners.get(type)?.delete(listener);
  }

  private emit(type: string, data: any): void {
    this.eventListeners.get(type)?.forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        console.error('Reading assistance event listener error:', error);
      }
    });
  }

  public destroy(): void {
    this.removeReadingGuide();
    this.removeReadingMask();
    this.removeFocusIndicator();
    this.stopPauseReminders();
    
    if (this.speechSynthesis) {
      this.speechSynthesis.cancel();
    }

    const style = document.getElementById('reading-assistance-styles');
    if (style) {
      style.remove();
    }

    document.documentElement.removeAttribute('data-reading-assistance');
    this.eventListeners.clear();
  }
}
