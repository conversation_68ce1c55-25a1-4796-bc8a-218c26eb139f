/**
 * PDF Viewer Configuration Management
 * Centralized configuration system with validation, persistence, and environment support
 */

export interface PDFViewerConfig {
  // Core Settings
  core: {
    enableWorkers: boolean;
    maxWorkers: number;
    enableRangeRequests: boolean;
    chunkSize: number;
    enableCompression: boolean;
    cacheSize: number;
  };

  // Performance Settings
  performance: {
    enableProgressiveLoading: boolean;
    enableMemoryOptimization: boolean;
    enableQualityAdaptation: boolean;
    renderingEngine: 'canvas' | 'svg' | 'webgl';
    maxMemoryUsage: number;
    gcThreshold: number;
  };

  // UI Settings
  ui: {
    theme: 'light' | 'dark' | 'auto' | 'high-contrast';
    showToolbar: boolean;
    showSidebar: boolean;
    showPageNumbers: boolean;
    showZoomControls: boolean;
    enableFullscreen: boolean;
    language: string;
  };

  // Accessibility Settings
  accessibility: {
    enableScreenReader: boolean;
    enableKeyboardNavigation: boolean;
    enableTextToSpeech: boolean;
    speechRate: number;
    speechPitch: number;
    speechVolume: number;
    enableHighContrast: boolean;
    enableLargeText: boolean;
    enableReducedMotion: boolean;
  };

  // Search Settings
  search: {
    enableSearch: boolean;
    enableRegexSearch: boolean;
    enableFuzzySearch: boolean;
    fuzzyThreshold: number;
    maxResults: number;
    highlightColor: string;
    enableSearchHistory: boolean;
  };

  // Mobile Settings
  mobile: {
    enableTouchGestures: boolean;
    enableHapticFeedback: boolean;
    enableAutoHideControls: boolean;
    touchSensitivity: number;
    gestureThresholds: {
      tap: number;
      doubleTap: number;
      longPress: number;
      swipe: number;
      pinch: number;
    };
  };

  // Security Settings
  security: {
    enableCSP: boolean;
    allowExternalResources: boolean;
    enableSandbox: boolean;
    maxFileSize: number;
    allowedDomains: string[];
  };

  // Analytics Settings
  analytics: {
    enableTracking: boolean;
    enablePerformanceMonitoring: boolean;
    enableErrorTracking: boolean;
    sampleRate: number;
    endpoint?: string;
    apiKey?: string;
  };

  // Developer Settings
  developer: {
    enableDebugMode: boolean;
    enableConsoleLogging: boolean;
    enablePerformanceMarks: boolean;
    showFPS: boolean;
    showMemoryUsage: boolean;
  };
}

export interface ConfigValidationRule {
  path: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  required?: boolean;
  min?: number;
  max?: number;
  enum?: unknown[];
  pattern?: RegExp;
  custom?: (value: unknown) => boolean | string;
}

const DEFAULT_CONFIG: PDFViewerConfig = {
  core: {
    enableWorkers: true,
    maxWorkers: Math.min(8, navigator.hardwareConcurrency || 4),
    enableRangeRequests: true,
    chunkSize: 1024 * 1024, // 1MB
    enableCompression: true,
    cacheSize: 100 * 1024 * 1024, // 100MB
  },

  performance: {
    enableProgressiveLoading: true,
    enableMemoryOptimization: true,
    enableQualityAdaptation: true,
    renderingEngine: 'canvas',
    maxMemoryUsage: 512 * 1024 * 1024, // 512MB
    gcThreshold: 0.8,
  },

  ui: {
    theme: 'auto',
    showToolbar: true,
    showSidebar: false,
    showPageNumbers: true,
    showZoomControls: true,
    enableFullscreen: true,
    language: 'en',
  },

  accessibility: {
    enableScreenReader: true,
    enableKeyboardNavigation: true,
    enableTextToSpeech: false,
    speechRate: 1.0,
    speechPitch: 1.0,
    speechVolume: 0.8,
    enableHighContrast: false,
    enableLargeText: false,
    enableReducedMotion: false,
  },

  search: {
    enableSearch: true,
    enableRegexSearch: true,
    enableFuzzySearch: true,
    fuzzyThreshold: 0.8,
    maxResults: 1000,
    highlightColor: '#ffff00',
    enableSearchHistory: true,
  },

  mobile: {
    enableTouchGestures: true,
    enableHapticFeedback: true,
    enableAutoHideControls: true,
    touchSensitivity: 1.0,
    gestureThresholds: {
      tap: 10,
      doubleTap: 300,
      longPress: 500,
      swipe: 50,
      pinch: 0.1,
    },
  },

  security: {
    enableCSP: true,
    allowExternalResources: false,
    enableSandbox: true,
    maxFileSize: 100 * 1024 * 1024, // 100MB
    allowedDomains: [],
  },

  analytics: {
    enableTracking: false,
    enablePerformanceMonitoring: true,
    enableErrorTracking: true,
    sampleRate: 1.0,
  },

  developer: {
    enableDebugMode: false,
    enableConsoleLogging: false,
    enablePerformanceMarks: false,
    showFPS: false,
    showMemoryUsage: false,
  },
};

const VALIDATION_RULES: ConfigValidationRule[] = [
  { path: 'core.maxWorkers', type: 'number', min: 1, max: 16 },
  { path: 'core.chunkSize', type: 'number', min: 64 * 1024, max: 10 * 1024 * 1024 },
  { path: 'performance.renderingEngine', type: 'string', enum: ['canvas', 'svg', 'webgl'] },
  { path: 'ui.theme', type: 'string', enum: ['light', 'dark', 'auto', 'high-contrast'] },
  { path: 'accessibility.speechRate', type: 'number', min: 0.1, max: 3.0 },
  { path: 'accessibility.speechPitch', type: 'number', min: 0, max: 2.0 },
  { path: 'accessibility.speechVolume', type: 'number', min: 0, max: 1.0 },
  { path: 'search.fuzzyThreshold', type: 'number', min: 0, max: 1.0 },
  { path: 'search.maxResults', type: 'number', min: 1, max: 10000 },
  { path: 'mobile.touchSensitivity', type: 'number', min: 0.1, max: 3.0 },
  { path: 'analytics.sampleRate', type: 'number', min: 0, max: 1.0 },
];

export class PDFConfigManager {
  private config: PDFViewerConfig;
  private listeners: Map<string, Set<(value: unknown) => void>> = new Map();
  private storageKey = 'pdf-viewer-config';

  constructor(initialConfig: Partial<PDFViewerConfig> = {}) {
    this.config = this.mergeConfig(DEFAULT_CONFIG, initialConfig);
    this.loadFromStorage();
    this.detectSystemPreferences();
    this.validateConfig();
  }

  private mergeConfig(base: PDFViewerConfig, override: Partial<PDFViewerConfig>): PDFViewerConfig {
    const merged = JSON.parse(JSON.stringify(base));
    
    for (const [key, value] of Object.entries(override)) {
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        merged[key] = { ...merged[key], ...value };
      } else {
        merged[key] = value;
      }
    }
    
    return merged;
  }

  private loadFromStorage(): void {
    try {
      const stored = localStorage.getItem(this.storageKey);
      if (stored) {
        const storedConfig = JSON.parse(stored);
        this.config = this.mergeConfig(this.config, storedConfig);
      }
    } catch (error) {
      console.warn('Failed to load config from storage:', error);
    }
  }

  private saveToStorage(): void {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(this.config));
    } catch (error) {
      console.warn('Failed to save config to storage:', error);
    }
  }

  private detectSystemPreferences(): void {
    // Detect theme preference
    if (this.config.ui.theme === 'auto') {
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      this.config.ui.theme = prefersDark ? 'dark' : 'light';
    }

    // Detect accessibility preferences
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      this.config.accessibility.enableReducedMotion = true;
    }

    if (window.matchMedia('(prefers-contrast: high)').matches) {
      this.config.accessibility.enableHighContrast = true;
    }

    // Detect mobile device
    const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    if (isMobile) {
      this.config.mobile.enableTouchGestures = true;
      this.config.ui.showSidebar = false; // Hide sidebar on mobile by default
    }

    // Listen for preference changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      if (this.config.ui.theme === 'auto') {
        this.set('ui.theme', e.matches ? 'dark' : 'light');
      }
    });
  }

  private validateConfig(): void {
    for (const rule of VALIDATION_RULES) {
      const value = this.get(rule.path);
      const error = this.validateValue(value, rule);
      
      if (error) {
        console.warn(`Config validation error for ${rule.path}: ${error}`);
        // Reset to default value
        const defaultValue = this.getDefaultValue(rule.path);
        this.set(rule.path, defaultValue);
      }
    }
  }

  private validateValue(value: unknown, rule: ConfigValidationRule): string | null {
    if (rule.required && (value === undefined || value === null)) {
      return 'Value is required';
    }

    if (value === undefined || value === null) {
      return null; // Optional value
    }

    if (rule.type === 'number') {
      if (typeof value !== 'number' || isNaN(value)) {
        return 'Value must be a number';
      }
      if (rule.min !== undefined && value < rule.min) {
        return `Value must be at least ${rule.min}`;
      }
      if (rule.max !== undefined && value > rule.max) {
        return `Value must be at most ${rule.max}`;
      }
    }

    if (rule.type === 'string') {
      if (typeof value !== 'string') {
        return 'Value must be a string';
      }
      if (rule.pattern && !rule.pattern.test(value)) {
        return 'Value does not match required pattern';
      }
    }

    if (rule.enum && !rule.enum.includes(value)) {
      return `Value must be one of: ${rule.enum.join(', ')}`;
    }

    if (rule.custom) {
      const result = rule.custom(value);
      if (typeof result === 'string') {
        return result;
      }
      if (!result) {
        return 'Value failed custom validation';
      }
    }

    return null;
  }

  private getDefaultValue(path: string): unknown {
    const keys = path.split('.');
    let value = DEFAULT_CONFIG;
    
    for (const key of keys) {
      value = value[key as keyof typeof value];
      if (value === undefined) break;
    }
    
    return value;
  }

  public get<T = unknown>(path: string): T {
    const keys = path.split('.');
    let value: unknown = this.config;
    
    for (const key of keys) {
      value = value?.[key];
      if (value === undefined) break;
    }
    
    return value;
  }

  public set(path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    let target: any = this.config;
    
    for (const key of keys) {
      if (!(key in target)) {
        target[key] = {};
      }
      target = target[key];
    }
    
    const oldValue = target[lastKey];
    target[lastKey] = value;
    
    // Validate the new value
    const rule = VALIDATION_RULES.find(r => r.path === path);
    if (rule) {
      const error = this.validateValue(value, rule);
      if (error) {
        target[lastKey] = oldValue; // Revert
        throw new Error(`Invalid value for ${path}: ${error}`);
      }
    }
    
    this.saveToStorage();
    this.notifyListeners(path, value);
  }

  public update(updates: Partial<PDFViewerConfig>): void {
    const oldConfig = JSON.parse(JSON.stringify(this.config));
    
    try {
      this.config = this.mergeConfig(this.config, updates);
      this.validateConfig();
      this.saveToStorage();
      
      // Notify listeners for all changed paths
      this.notifyAllListeners();
    } catch (error) {
      this.config = oldConfig; // Revert on error
      throw error;
    }
  }

  public reset(section?: keyof PDFViewerConfig): void {
    if (section) {
      this.config[section] = JSON.parse(JSON.stringify(DEFAULT_CONFIG[section]));
    } else {
      this.config = JSON.parse(JSON.stringify(DEFAULT_CONFIG));
    }
    
    this.saveToStorage();
    this.notifyAllListeners();
  }

  public getConfig(): PDFViewerConfig {
    return JSON.parse(JSON.stringify(this.config));
  }

  public exportConfig(): string {
    return JSON.stringify(this.config, null, 2);
  }

  public importConfig(configJson: string): void {
    try {
      const imported = JSON.parse(configJson);
      this.update(imported);
    } catch (error) {
      throw new Error('Invalid configuration JSON');
    }
  }

  public subscribe(path: string, listener: (value: any) => void): () => void {
    if (!this.listeners.has(path)) {
      this.listeners.set(path, new Set());
    }
    
    this.listeners.get(path)!.add(listener);
    
    // Return unsubscribe function
    return () => {
      this.listeners.get(path)?.delete(listener);
    };
  }

  private notifyListeners(path: string, value: any): void {
    this.listeners.get(path)?.forEach(listener => {
      try {
        listener(value);
      } catch (error) {
        console.error('Config listener error:', error);
      }
    });
  }

  private notifyAllListeners(): void {
    for (const [path, listeners] of this.listeners) {
      const value = this.get(path);
      listeners.forEach(listener => {
        try {
          listener(value);
        } catch (error) {
          console.error('Config listener error:', error);
        }
      });
    }
  }

  public destroy(): void {
    this.listeners.clear();
  }
}
