/**
 * Browser Utilities
 * Helper functions for browser environment checks and safe API usage
 */

export const isBrowser = (): boolean => {
  return typeof window !== 'undefined' && typeof document !== 'undefined';
};

export const isLocalStorageAvailable = (): boolean => {
  if (!isBrowser()) return false;
  
  try {
    const test = '__localStorage_test__';
    localStorage.setItem(test, test);
    localStorage.removeItem(test);
    return true;
  } catch {
    return false;
  }
};

export const isSessionStorageAvailable = (): boolean => {
  if (!isBrowser()) return false;
  
  try {
    const test = '__sessionStorage_test__';
    sessionStorage.setItem(test, test);
    sessionStorage.removeItem(test);
    return true;
  } catch {
    return false;
  }
};

export const isSpeechSynthesisAvailable = (): boolean => {
  return isBrowser() && 'speechSynthesis' in window;
};

export const isWebWorkersAvailable = (): boolean => {
  return isBrowser() && 'Worker' in window;
};

export const isIntersectionObserverAvailable = (): boolean => {
  return isBrowser() && 'IntersectionObserver' in window;
};

export const isResizeObserverAvailable = (): boolean => {
  return isBrowser() && 'ResizeObserver' in window;
};

export const safeLocalStorageGetItem = (key: string): string | null => {
  if (!isLocalStorageAvailable()) return null;
  
  try {
    return localStorage.getItem(key);
  } catch {
    return null;
  }
};

export const safeLocalStorageSetItem = (key: string, value: string): boolean => {
  if (!isLocalStorageAvailable()) return false;
  
  try {
    localStorage.setItem(key, value);
    return true;
  } catch {
    return false;
  }
};

export const safeLocalStorageRemoveItem = (key: string): boolean => {
  if (!isLocalStorageAvailable()) return false;
  
  try {
    localStorage.removeItem(key);
    return true;
  } catch {
    return false;
  }
};

export const safeSessionStorageGetItem = (key: string): string | null => {
  if (!isSessionStorageAvailable()) return null;
  
  try {
    return sessionStorage.getItem(key);
  } catch {
    return null;
  }
};

export const safeSessionStorageSetItem = (key: string, value: string): boolean => {
  if (!isSessionStorageAvailable()) return false;
  
  try {
    sessionStorage.setItem(key, value);
    return true;
  } catch {
    return false;
  }
};

export const safeJSONParse = <T = any>(value: string | null, fallback: T): T => {
  if (!value) return fallback;
  
  try {
    const parsed = JSON.parse(value);
    return parsed !== null && parsed !== undefined ? parsed : fallback;
  } catch {
    return fallback;
  }
};

export const safeJSONStringify = (value: any): string | null => {
  try {
    return JSON.stringify(value);
  } catch {
    return null;
  }
};

export const createSafeEventListener = (
  element: EventTarget | null,
  event: string,
  handler: EventListener,
  options?: boolean | AddEventListenerOptions
): (() => void) => {
  if (!element || !isBrowser()) {
    return () => {}; // Return no-op cleanup function
  }
  
  try {
    element.addEventListener(event, handler, options);
    return () => {
      try {
        element.removeEventListener(event, handler, options);
      } catch {
        // Ignore cleanup errors
      }
    };
  } catch {
    return () => {}; // Return no-op cleanup function
  }
};

export const safeQuerySelector = (selector: string): Element | null => {
  if (!isBrowser()) return null;
  
  try {
    return document.querySelector(selector);
  } catch {
    return null;
  }
};

export const safeQuerySelectorAll = (selector: string): NodeListOf<Element> | null => {
  if (!isBrowser()) return null;
  
  try {
    return document.querySelectorAll(selector);
  } catch {
    return null;
  }
};

export const safeGetElementById = (id: string): HTMLElement | null => {
  if (!isBrowser()) return null;
  
  try {
    return document.getElementById(id);
  } catch {
    return null;
  }
};

export const safeCreateElement = (tagName: string): HTMLElement | null => {
  if (!isBrowser()) return null;
  
  try {
    return document.createElement(tagName);
  } catch {
    return null;
  }
};

export const safeAppendChild = (parent: Node | null, child: Node | null): boolean => {
  if (!parent || !child || !isBrowser()) return false;
  
  try {
    parent.appendChild(child);
    return true;
  } catch {
    return false;
  }
};

export const safeRemoveChild = (parent: Node | null, child: Node | null): boolean => {
  if (!parent || !child || !isBrowser()) return false;
  
  try {
    parent.removeChild(child);
    return true;
  } catch {
    return false;
  }
};

export const safeSetTimeout = (callback: () => void, delay: number): number | null => {
  if (!isBrowser()) return null;
  
  try {
    return window.setTimeout(callback, delay);
  } catch {
    return null;
  }
};

export const safeClearTimeout = (timeoutId: number | null): void => {
  if (!timeoutId || !isBrowser()) return;
  
  try {
    window.clearTimeout(timeoutId);
  } catch {
    // Ignore errors
  }
};

export const safeSetInterval = (callback: () => void, delay: number): number | null => {
  if (!isBrowser()) return null;
  
  try {
    return window.setInterval(callback, delay);
  } catch {
    return null;
  }
};

export const safeClearInterval = (intervalId: number | null): void => {
  if (!intervalId || !isBrowser()) return;
  
  try {
    window.clearInterval(intervalId);
  } catch {
    // Ignore errors
  }
};

export const getViewportDimensions = (): { width: number; height: number } => {
  if (!isBrowser()) return { width: 0, height: 0 };
  
  return {
    width: window.innerWidth || document.documentElement.clientWidth || 0,
    height: window.innerHeight || document.documentElement.clientHeight || 0,
  };
};

export const getScrollPosition = (): { x: number; y: number } => {
  if (!isBrowser()) return { x: 0, y: 0 };
  
  return {
    x: window.pageXOffset || document.documentElement.scrollLeft || 0,
    y: window.pageYOffset || document.documentElement.scrollTop || 0,
  };
};

export const isElementInViewport = (element: Element): boolean => {
  if (!isBrowser() || !element) return false;
  
  try {
    const rect = element.getBoundingClientRect();
    const viewport = getViewportDimensions();
    
    return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <= viewport.height &&
      rect.right <= viewport.width
    );
  } catch {
    return false;
  }
};

export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: number | null = null;
  
  return (...args: Parameters<T>) => {
    if (timeout !== null) {
      safeClearTimeout(timeout);
    }
    
    timeout = safeSetTimeout(() => {
      func(...args);
    }, wait);
  };
};

export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean = false;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      safeSetTimeout(() => {
        inThrottle = false;
      }, limit);
    }
  };
};

export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validateURL = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

export const sanitizeHTML = (html: string): string => {
  if (!isBrowser()) return '';
  
  try {
    const div = document.createElement('div');
    div.textContent = html;
    return div.innerHTML;
  } catch {
    return '';
  }
};

export const copyToClipboard = async (text: string): Promise<boolean> => {
  if (!isBrowser()) return false;
  
  try {
    if (navigator.clipboard && navigator.clipboard.writeText) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      
      const result = document.execCommand('copy');
      document.body.removeChild(textArea);
      return result;
    }
  } catch {
    return false;
  }
};

export const downloadFile = (content: string, filename: string, mimeType: string = 'text/plain'): boolean => {
  if (!isBrowser()) return false;
  
  try {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.style.display = 'none';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
    return true;
  } catch {
    return false;
  }
};
