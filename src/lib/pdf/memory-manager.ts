/**
 * PDF Memory Manager & Intelligent Caching System
 * Advanced memory management with LRU caching, compression, and automatic cleanup
 */

export interface MemoryConfig {
  maxMemoryMB: number;
  maxCacheItems: number;
  compressionEnabled: boolean;
  compressionThreshold: number; // bytes
  gcThreshold: number; // percentage of max memory
  preloadStrategy: 'none' | 'adjacent' | 'smart';
  enableMetrics: boolean;
  cleanupInterval: number; // ms
}

export interface CacheItem {
  id: string;
  type: 'page' | 'image' | 'text' | 'annotation';
  data: any;
  size: number;
  compressed: boolean;
  lastAccessed: number;
  accessCount: number;
  priority: number;
  metadata: Record<string, any>;
}

export interface MemoryMetrics {
  totalMemoryUsed: number;
  cacheHitRate: number;
  compressionRatio: number;
  itemCount: number;
  averageItemSize: number;
  gcCount: number;
  lastCleanup: number;
  memoryPressure: 'low' | 'medium' | 'high' | 'critical';
}

export interface MemoryPressureEvent {
  type: 'memory-pressure';
  level: 'medium' | 'high' | 'critical';
  currentUsage: number;
  maxMemory: number;
  suggestedActions: string[];
}

const DEFAULT_CONFIG: MemoryConfig = {
  maxMemoryMB: 512,
  maxCacheItems: 1000,
  compressionEnabled: true,
  compressionThreshold: 1024 * 1024, // 1MB
  gcThreshold: 80, // 80% of max memory
  preloadStrategy: 'smart',
  enableMetrics: true,
  cleanupInterval: 30000, // 30 seconds
};

export class MemoryManager {
  private config: MemoryConfig;
  private cache: Map<string, CacheItem> = new Map();
  private accessOrder: string[] = [];
  private metrics: MemoryMetrics;
  private cleanupTimer: number | null = null;
  private compressionWorker: Worker | null = null;
  private eventListeners: Map<string, Set<(event: any) => void>> = new Map();

  constructor(config: Partial<MemoryConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.metrics = this.initializeMetrics();
    this.initializeCompressionWorker();
    this.startCleanupTimer();
    this.setupMemoryPressureDetection();
  }

  private initializeMetrics(): MemoryMetrics {
    return {
      totalMemoryUsed: 0,
      cacheHitRate: 0,
      compressionRatio: 0,
      itemCount: 0,
      averageItemSize: 0,
      gcCount: 0,
      lastCleanup: Date.now(),
      memoryPressure: 'low',
    };
  }

  private initializeCompressionWorker(): void {
    if (!this.config.compressionEnabled) return;

    try {
      // Create compression worker for large items
      const workerCode = `
        self.onmessage = function(e) {
          const { id, data, compress } = e.data;
          
          if (compress) {
            // Simple compression simulation (in real implementation, use proper compression)
            const compressed = JSON.stringify(data);
            self.postMessage({ id, compressed, originalSize: data.length || 0, compressedSize: compressed.length });
          } else {
            // Decompression
            const decompressed = JSON.parse(data);
            self.postMessage({ id, decompressed });
          }
        };
      `;
      
      const blob = new Blob([workerCode], { type: 'application/javascript' });
      this.compressionWorker = new Worker(URL.createObjectURL(blob));
    } catch (error) {
      console.warn('Failed to create compression worker:', error);
      this.config.compressionEnabled = false;
    }
  }

  private setupMemoryPressureDetection(): void {
    // Monitor memory pressure using various indicators
    if ('memory' in performance) {
      setInterval(() => {
        const memInfo = (performance as any).memory;
        const usedPercent = (memInfo.usedJSHeapSize / memInfo.jsHeapSizeLimit) * 100;
        
        let pressureLevel: MemoryMetrics['memoryPressure'] = 'low';
        if (usedPercent > 90) pressureLevel = 'critical';
        else if (usedPercent > 75) pressureLevel = 'high';
        else if (usedPercent > 60) pressureLevel = 'medium';
        
        if (pressureLevel !== this.metrics.memoryPressure) {
          this.metrics.memoryPressure = pressureLevel;
          this.handleMemoryPressure(pressureLevel);
        }
      }, 5000);
    }
  }

  private handleMemoryPressure(level: MemoryMetrics['memoryPressure']): void {
    if (level === 'low') return;

    const event: MemoryPressureEvent = {
      type: 'memory-pressure',
      level: level as any,
      currentUsage: this.metrics.totalMemoryUsed,
      maxMemory: this.config.maxMemoryMB * 1024 * 1024,
      suggestedActions: this.getSuggestedActions(level),
    };

    this.emit('memory-pressure', event);

    // Auto-cleanup based on pressure level
    if (level === 'critical') {
      this.forceCleanup(0.5); // Remove 50% of cache
    } else if (level === 'high') {
      this.forceCleanup(0.3); // Remove 30% of cache
    } else if (level === 'medium') {
      this.cleanup();
    }
  }

  private getSuggestedActions(level: MemoryMetrics['memoryPressure']): string[] {
    const actions = [];
    
    if (level === 'critical') {
      actions.push('Reduce render quality');
      actions.push('Clear all caches');
      actions.push('Disable preloading');
      actions.push('Enable aggressive compression');
    } else if (level === 'high') {
      actions.push('Clear old cache items');
      actions.push('Reduce preload count');
      actions.push('Enable compression');
    } else if (level === 'medium') {
      actions.push('Clean up unused items');
      actions.push('Compress large items');
    }
    
    return actions;
  }

  public async store(id: string, data: any, options: {
    type?: CacheItem['type'];
    priority?: number;
    metadata?: Record<string, any>;
    compress?: boolean;
  } = {}): Promise<boolean> {
    const size = this.estimateSize(data);
    const shouldCompress = options.compress ?? (
      this.config.compressionEnabled && 
      size > this.config.compressionThreshold
    );

    // Check if we have enough memory
    if (!this.canAllocate(size)) {
      await this.makeSpace(size);
    }

    let finalData = data;
    let finalSize = size;
    let compressed = false;

    // Compress if needed
    if (shouldCompress && this.compressionWorker) {
      try {
        const compressedData = await this.compressData(data);
        if (compressedData.compressedSize < size * 0.8) { // Only use if 20%+ savings
          finalData = compressedData.compressed;
          finalSize = compressedData.compressedSize;
          compressed = true;
        }
      } catch (error) {
        console.warn('Compression failed:', error);
      }
    }

    const item: CacheItem = {
      id,
      type: options.type || 'page',
      data: finalData,
      size: finalSize,
      compressed,
      lastAccessed: Date.now(),
      accessCount: 1,
      priority: options.priority || 1,
      metadata: options.metadata || {},
    };

    // Remove existing item if present
    if (this.cache.has(id)) {
      this.remove(id);
    }

    // Add to cache
    this.cache.set(id, item);
    this.updateAccessOrder(id);
    this.updateMetrics();

    return true;
  }

  public async get(id: string): Promise<any | null> {
    const item = this.cache.get(id);
    if (!item) {
      return null;
    }

    // Update access information
    item.lastAccessed = Date.now();
    item.accessCount++;
    this.updateAccessOrder(id);

    // Decompress if needed
    if (item.compressed && this.compressionWorker) {
      try {
        const decompressed = await this.decompressData(item.data);
        return decompressed;
      } catch (error) {
        console.warn('Decompression failed:', error);
        return null;
      }
    }

    return item.data;
  }

  public remove(id: string): boolean {
    const item = this.cache.get(id);
    if (!item) return false;

    this.cache.delete(id);
    this.removeFromAccessOrder(id);
    this.updateMetrics();

    return true;
  }

  public clear(): void {
    this.cache.clear();
    this.accessOrder = [];
    this.updateMetrics();
  }

  private async compressData(data: any): Promise<{ compressed: any; compressedSize: number }> {
    return new Promise((resolve, reject) => {
      if (!this.compressionWorker) {
        reject(new Error('Compression worker not available'));
        return;
      }

      const timeout = setTimeout(() => {
        reject(new Error('Compression timeout'));
      }, 5000);

      const handler = (e: MessageEvent) => {
        clearTimeout(timeout);
        this.compressionWorker!.removeEventListener('message', handler);
        resolve({ compressed: e.data.compressed, compressedSize: e.data.compressedSize });
      };

      this.compressionWorker.addEventListener('message', handler);
      this.compressionWorker.postMessage({ id: Math.random().toString(), data, compress: true });
    });
  }

  private async decompressData(data: any): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.compressionWorker) {
        reject(new Error('Compression worker not available'));
        return;
      }

      const timeout = setTimeout(() => {
        reject(new Error('Decompression timeout'));
      }, 5000);

      const handler = (e: MessageEvent) => {
        clearTimeout(timeout);
        this.compressionWorker!.removeEventListener('message', handler);
        resolve(e.data.decompressed);
      };

      this.compressionWorker.addEventListener('message', handler);
      this.compressionWorker.postMessage({ id: Math.random().toString(), data, compress: false });
    });
  }

  private estimateSize(data: any): number {
    if (data instanceof ArrayBuffer) {
      return data.byteLength;
    }
    
    if (data instanceof ImageData) {
      return data.data.length;
    }
    
    if (typeof data === 'string') {
      return data.length * 2; // UTF-16
    }
    
    if (data && typeof data === 'object') {
      try {
        return JSON.stringify(data).length * 2;
      } catch {
        return 1024; // Fallback estimate
      }
    }
    
    return 64; // Default estimate for primitives
  }

  private canAllocate(size: number): boolean {
    const maxMemory = this.config.maxMemoryMB * 1024 * 1024;
    return (this.metrics.totalMemoryUsed + size) <= maxMemory;
  }

  private async makeSpace(requiredSize: number): Promise<void> {
    const maxMemory = this.config.maxMemoryMB * 1024 * 1024;
    const targetSize = maxMemory - requiredSize;
    
    // Remove items in LRU order until we have enough space
    while (this.metrics.totalMemoryUsed > targetSize && this.accessOrder.length > 0) {
      const oldestId = this.accessOrder[0];
      this.remove(oldestId);
    }
  }

  private forceCleanup(percentage: number): void {
    const itemsToRemove = Math.floor(this.cache.size * percentage);
    
    for (let i = 0; i < itemsToRemove && this.accessOrder.length > 0; i++) {
      const oldestId = this.accessOrder[0];
      this.remove(oldestId);
    }
    
    this.metrics.gcCount++;
  }

  private cleanup(): void {
    const now = Date.now();
    const maxAge = 5 * 60 * 1000; // 5 minutes
    
    // Remove old items
    for (const [id, item] of this.cache) {
      if (now - item.lastAccessed > maxAge && item.accessCount < 2) {
        this.remove(id);
      }
    }
    
    // Check if we need more aggressive cleanup
    const memoryUsagePercent = (this.metrics.totalMemoryUsed / (this.config.maxMemoryMB * 1024 * 1024)) * 100;
    if (memoryUsagePercent > this.config.gcThreshold) {
      this.forceCleanup(0.2); // Remove 20% of items
    }
    
    this.metrics.lastCleanup = now;
  }

  private updateAccessOrder(id: string): void {
    this.removeFromAccessOrder(id);
    this.accessOrder.push(id);
  }

  private removeFromAccessOrder(id: string): void {
    const index = this.accessOrder.indexOf(id);
    if (index > -1) {
      this.accessOrder.splice(index, 1);
    }
  }

  private updateMetrics(): void {
    let totalSize = 0;
    let compressedSize = 0;
    let uncompressedSize = 0;
    
    for (const item of this.cache.values()) {
      totalSize += item.size;
      if (item.compressed) {
        compressedSize += item.size;
      } else {
        uncompressedSize += item.size;
      }
    }
    
    this.metrics.totalMemoryUsed = totalSize;
    this.metrics.itemCount = this.cache.size;
    this.metrics.averageItemSize = this.cache.size > 0 ? totalSize / this.cache.size : 0;
    this.metrics.compressionRatio = compressedSize > 0 ? compressedSize / (compressedSize + uncompressedSize) : 0;
  }

  private startCleanupTimer(): void {
    this.cleanupTimer = window.setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  public addEventListener(type: string, listener: (event: any) => void): void {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, new Set());
    }
    this.eventListeners.get(type)!.add(listener);
  }

  public removeEventListener(type: string, listener: (event: any) => void): void {
    this.eventListeners.get(type)?.delete(listener);
  }

  private emit(type: string, event: any): void {
    this.eventListeners.get(type)?.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in memory manager event listener:', error);
      }
    });
  }

  public getMetrics(): MemoryMetrics {
    return { ...this.metrics };
  }

  public updateConfig(newConfig: Partial<MemoryConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  public has(id: string): boolean {
    return this.cache.has(id);
  }

  public getSize(): number {
    return this.cache.size;
  }

  public getMemoryUsage(): number {
    return this.metrics.totalMemoryUsed;
  }

  public destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
    
    if (this.compressionWorker) {
      this.compressionWorker.terminate();
      this.compressionWorker = null;
    }
    
    this.clear();
    this.eventListeners.clear();
  }
}
