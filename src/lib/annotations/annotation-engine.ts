/**
 * Advanced Annotation Engine
 * Comprehensive annotation system with rich drawing tools, stamps, signatures, and multimedia support
 */

import type { Annotation, AnnotationType } from '@/components/annotations/pdf-annotations';

export interface AdvancedAnnotation extends Annotation {
  // Enhanced visual properties
  fillColor?: string;
  strokeStyle?: 'solid' | 'dashed' | 'dotted';
  strokeDashArray?: number[];
  borderRadius?: number;
  shadow?: {
    offsetX: number;
    offsetY: number;
    blur: number;
    color: string;
  };
  
  // Advanced shape properties
  rotation?: number;
  skewX?: number;
  skewY?: number;
  scaleX?: number;
  scaleY?: number;
  
  // Rich content
  richText?: {
    html: string;
    plainText: string;
    formatting: {
      bold?: boolean;
      italic?: boolean;
      underline?: boolean;
      strikethrough?: boolean;
      fontSize?: number;
      fontFamily?: string;
      textAlign?: 'left' | 'center' | 'right' | 'justify';
      lineHeight?: number;
      letterSpacing?: number;
    };
  };
  
  // Multimedia content
  multimedia?: {
    type: 'image' | 'video' | 'audio' | 'file';
    url: string;
    thumbnailUrl?: string;
    filename?: string;
    fileSize?: number;
    mimeType?: string;
    duration?: number; // for video/audio
    dimensions?: { width: number; height: number };
  };
  
  // Signature properties
  signature?: {
    type: 'drawn' | 'typed' | 'image' | 'digital';
    data: string; // SVG path, image data, or certificate data
    signedBy?: string;
    signedAt?: Date;
    certificate?: {
      issuer: string;
      subject: string;
      validFrom: Date;
      validTo: Date;
      fingerprint: string;
    };
  };
  
  // Stamp properties
  stamp?: {
    type: 'predefined' | 'custom' | 'dynamic';
    template: string;
    variables?: Record<string, string>;
    size: 'small' | 'medium' | 'large';
  };
  
  // Measurement properties
  measurement?: {
    type: 'distance' | 'area' | 'perimeter' | 'angle';
    value: number;
    unit: string;
    precision: number;
    calibration?: {
      pixelsPerUnit: number;
      referenceLength: number;
    };
  };
  
  // Form field properties
  formField?: {
    type: 'text' | 'checkbox' | 'radio' | 'dropdown' | 'signature';
    name: string;
    value?: string | boolean | string[];
    required?: boolean;
    readonly?: boolean;
    validation?: {
      pattern?: string;
      minLength?: number;
      maxLength?: number;
      min?: number;
      max?: number;
    };
    options?: string[]; // for dropdown/radio
  };
  
  // Collaboration properties
  thread?: {
    id: string;
    status: 'open' | 'resolved' | 'closed';
    priority: 'low' | 'medium' | 'high' | 'urgent';
    assignee?: string;
    dueDate?: Date;
    category?: string;
  };
  
  // Animation properties
  animation?: {
    type: 'fade' | 'slide' | 'bounce' | 'pulse';
    duration: number;
    delay?: number;
    repeat?: number | 'infinite';
    direction?: 'normal' | 'reverse' | 'alternate';
  };
  
  // Conditional visibility
  visibility?: {
    conditions: Array<{
      type: 'user' | 'role' | 'date' | 'custom';
      operator: 'equals' | 'not_equals' | 'contains' | 'before' | 'after';
      value: string;
    }>;
    defaultVisible: boolean;
  };
  
  // Layer and grouping
  layer?: number;
  group?: string;
  zIndex?: number;
  
  // Interaction properties
  interactive?: {
    clickable?: boolean;
    hoverable?: boolean;
    draggable?: boolean;
    resizable?: boolean;
    rotatable?: boolean;
    onClick?: string; // JavaScript code or action
    onHover?: string;
  };
}

export interface AnnotationTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  thumbnail: string;
  template: Partial<AdvancedAnnotation>;
  variables?: Array<{
    name: string;
    type: 'text' | 'number' | 'color' | 'date' | 'boolean';
    label: string;
    defaultValue?: string | number | boolean | Date;
    required?: boolean;
  }>;
}

export interface DrawingPath {
  id: string;
  points: Array<{ x: number; y: number; pressure?: number; timestamp?: number }>;
  tool: 'pen' | 'pencil' | 'brush' | 'marker' | 'eraser';
  color: string;
  width: number;
  opacity: number;
  smoothing?: number;
  stabilization?: number;
}

export interface AnnotationStyle {
  fill?: {
    color: string;
    opacity: number;
    gradient?: {
      type: 'linear' | 'radial';
      stops: Array<{ offset: number; color: string }>;
      angle?: number;
    };
    pattern?: {
      type: 'dots' | 'lines' | 'grid' | 'diagonal';
      spacing: number;
      color: string;
    };
  };
  stroke?: {
    color: string;
    width: number;
    opacity: number;
    style: 'solid' | 'dashed' | 'dotted';
    dashArray?: number[];
    lineCap: 'butt' | 'round' | 'square';
    lineJoin: 'miter' | 'round' | 'bevel';
  };
  text?: {
    fontFamily: string;
    fontSize: number;
    fontWeight: 'normal' | 'bold' | '100' | '200' | '300' | '400' | '500' | '600' | '700' | '800' | '900';
    fontStyle: 'normal' | 'italic' | 'oblique';
    textDecoration: 'none' | 'underline' | 'overline' | 'line-through';
    textAlign: 'left' | 'center' | 'right' | 'justify';
    lineHeight: number;
    letterSpacing: number;
    wordSpacing: number;
  };
  effects?: {
    shadow?: {
      offsetX: number;
      offsetY: number;
      blur: number;
      color: string;
    };
    glow?: {
      color: string;
      size: number;
      intensity: number;
    };
    blur?: {
      amount: number;
    };
    opacity?: number;
  };
}

export class AnnotationEngine {
  private annotations: Map<string, AdvancedAnnotation> = new Map();
  private templates: Map<string, AnnotationTemplate> = new Map();
  private history: Array<{ action: string; annotation: AdvancedAnnotation; timestamp: Date }> = [];
  private currentHistoryIndex = -1;
  private maxHistorySize = 100;
  
  constructor() {
    this.loadDefaultTemplates();
  }
  
  /**
   * Create a new annotation
   */
  createAnnotation(
    type: AnnotationType,
    pageNumber: number,
    position: { x: number; y: number },
    style?: Partial<AnnotationStyle>,
    options?: Partial<AdvancedAnnotation>
  ): AdvancedAnnotation {
    const id = this.generateId();
    const now = new Date();
    
    const annotation: AdvancedAnnotation = {
      id,
      type,
      pageNumber,
      x: position.x,
      y: position.y,
      width: options?.width || this.getDefaultSize(type).width,
      height: options?.height || this.getDefaultSize(type).height,
      color: style?.stroke?.color || style?.fill?.color || '#FF6B6B',
      strokeColor: style?.stroke?.color,
      strokeWidth: style?.stroke?.width || 2,
      opacity: style?.effects?.opacity || 1,
      author: 'Current User', // Would come from auth context
      createdAt: now,
      updatedAt: now,
      isVisible: true,
      isLocked: false,
      layer: 0,
      zIndex: this.getNextZIndex(),
      ...options,
    };
    
    this.annotations.set(id, annotation);
    this.addToHistory('create', annotation);
    
    return annotation;
  }
  
  /**
   * Update an existing annotation
   */
  updateAnnotation(id: string, updates: Partial<AdvancedAnnotation>): AdvancedAnnotation | null {
    const annotation = this.annotations.get(id);
    if (!annotation) return null;
    
    const updatedAnnotation: AdvancedAnnotation = {
      ...annotation,
      ...updates,
      updatedAt: new Date(),
    };

    // Ensure updatedAt is actually different by adding a small delay if needed
    if (updatedAnnotation.updatedAt.getTime() === annotation.updatedAt.getTime()) {
      updatedAnnotation.updatedAt = new Date(annotation.updatedAt.getTime() + 1);
    }
    
    this.annotations.set(id, updatedAnnotation);
    this.addToHistory('update', updatedAnnotation);
    
    return updatedAnnotation;
  }
  
  /**
   * Delete an annotation
   */
  deleteAnnotation(id: string): boolean {
    const annotation = this.annotations.get(id);
    if (!annotation) return false;
    
    this.annotations.delete(id);
    this.addToHistory('delete', annotation);
    
    return true;
  }
  
  /**
   * Get annotation by ID
   */
  getAnnotation(id: string): AdvancedAnnotation | null {
    return this.annotations.get(id) || null;
  }
  
  /**
   * Get all annotations for a page
   */
  getAnnotationsForPage(pageNumber: number): AdvancedAnnotation[] {
    return Array.from(this.annotations.values())
      .filter(annotation => annotation.pageNumber === pageNumber)
      .sort((a, b) => (a.zIndex || 0) - (b.zIndex || 0));
  }
  
  /**
   * Get all annotations
   */
  getAllAnnotations(): AdvancedAnnotation[] {
    return Array.from(this.annotations.values());
  }
  
  /**
   * Create annotation from template
   */
  createFromTemplate(
    templateId: string,
    pageNumber: number,
    position: { x: number; y: number },
    variables?: Record<string, any>
  ): AdvancedAnnotation | null {
    const template = this.templates.get(templateId);
    if (!template) return null;
    
    let templateData = { ...template.template };
    
    // Apply variables if provided
    if (variables && template.variables) {
      templateData = this.applyTemplateVariables(templateData, variables, template.variables);
    }
    
    return this.createAnnotation(
      templateData.type || 'text',
      pageNumber,
      position,
      undefined,
      templateData
    );
  }
  
  /**
   * Duplicate an annotation
   */
  duplicateAnnotation(id: string, offset: { x: number; y: number } = { x: 10, y: 10 }): AdvancedAnnotation | null {
    const original = this.annotations.get(id);
    if (!original) return null;
    
    const duplicate = {
      ...original,
      id: this.generateId(),
      x: original.x + offset.x,
      y: original.y + offset.y,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    this.annotations.set(duplicate.id, duplicate);
    this.addToHistory('create', duplicate);
    
    return duplicate;
  }
  
  /**
   * Group annotations
   */
  groupAnnotations(annotationIds: string[], groupName?: string): string {
    const groupId = groupName || this.generateId();
    
    annotationIds.forEach(id => {
      const annotation = this.annotations.get(id);
      if (annotation) {
        this.updateAnnotation(id, { group: groupId });
      }
    });
    
    return groupId;
  }
  
  /**
   * Ungroup annotations
   */
  ungroupAnnotations(groupId: string): void {
    Array.from(this.annotations.values())
      .filter(annotation => annotation.group === groupId)
      .forEach(annotation => {
        this.updateAnnotation(annotation.id, { group: undefined });
      });
  }
  
  /**
   * Undo last action
   */
  undo(): AdvancedAnnotation | null {
    if (this.currentHistoryIndex < 0) return null;

    const historyItem = this.history[this.currentHistoryIndex];
    this.currentHistoryIndex--;

    // Reverse the action
    switch (historyItem.action) {
      case 'create':
        this.annotations.delete(historyItem.annotation.id);
        break;
      case 'delete':
        this.annotations.set(historyItem.annotation.id, historyItem.annotation);
        break;
      case 'update':
        // Would need to store previous state for proper undo
        break;
    }

    return historyItem.annotation;
  }
  
  /**
   * Redo last undone action
   */
  redo(): AdvancedAnnotation | null {
    if (this.currentHistoryIndex >= this.history.length - 1) return null;

    this.currentHistoryIndex++;
    const historyItem = this.history[this.currentHistoryIndex];

    // Reapply the action
    switch (historyItem.action) {
      case 'create':
        this.annotations.set(historyItem.annotation.id, historyItem.annotation);
        break;
      case 'delete':
        this.annotations.delete(historyItem.annotation.id);
        break;
      case 'update':
        this.annotations.set(historyItem.annotation.id, historyItem.annotation);
        break;
    }

    return historyItem.annotation;
  }
  
  /**
   * Clear all annotations
   */
  clearAll(): void {
    this.annotations.clear();
    this.history = [];
    this.currentHistoryIndex = -1;
  }
  
  /**
   * Export annotations to various formats
   */
  exportAnnotations(format: 'json' | 'pdf' | 'xfdf' | 'csv'): string {
    const annotations = this.getAllAnnotations();
    
    switch (format) {
      case 'json':
        return JSON.stringify(annotations, null, 2);
      case 'csv':
        return this.exportToCSV(annotations);
      case 'xfdf':
        return this.exportToXFDF(annotations);
      default:
        return JSON.stringify(annotations);
    }
  }
  
  /**
   * Import annotations from various formats
   */
  importAnnotations(data: string, format: 'json' | 'xfdf'): number {
    let importedAnnotations: AdvancedAnnotation[] = [];
    
    try {
      switch (format) {
        case 'json':
          importedAnnotations = JSON.parse(data);
          break;
        case 'xfdf':
          importedAnnotations = this.parseXFDF(data);
          break;
      }
      
      importedAnnotations.forEach(annotation => {
        annotation.id = this.generateId(); // Ensure unique IDs
        this.annotations.set(annotation.id, annotation);
      });
      
      return importedAnnotations.length;
    } catch (error) {
      console.error('Failed to import annotations:', error);
      return 0;
    }
  }
  
  // Private helper methods
  
  private generateId(): string {
    return `ann_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  private getDefaultSize(type: AnnotationType): { width: number; height: number } {
    const sizes = {
      text: { width: 200, height: 30 },
      note: { width: 20, height: 20 },
      rectangle: { width: 100, height: 60 },
      circle: { width: 80, height: 80 },
      line: { width: 100, height: 2 },
      arrow: { width: 100, height: 20 },
      highlight: { width: 100, height: 20 },
      freehand: { width: 0, height: 0 },
      stamp: { width: 60, height: 40 },
      signature: { width: 150, height: 50 },
    };
    
    return sizes[type] || { width: 100, height: 100 };
  }
  
  private getNextZIndex(): number {
    const maxZ = Math.max(0, ...Array.from(this.annotations.values()).map(a => a.zIndex || 0));
    return maxZ + 1;
  }
  
  private addToHistory(action: string, annotation: AdvancedAnnotation): void {
    // Remove any history items after current index (for redo functionality)
    this.history = this.history.slice(0, this.currentHistoryIndex + 1);
    
    this.history.push({ action, annotation: { ...annotation }, timestamp: new Date() });
    this.currentHistoryIndex++;
    
    // Limit history size
    if (this.history.length > this.maxHistorySize) {
      this.history = this.history.slice(-this.maxHistorySize);
      this.currentHistoryIndex = this.history.length - 1;
    }
  }
  
  private applyTemplateVariables(
    template: Partial<AdvancedAnnotation>,
    variables: Record<string, any>,
    templateVariables: AnnotationTemplate['variables']
  ): Partial<AdvancedAnnotation> {
    // Apply variable substitution to template
    const result = { ...template };
    
    templateVariables?.forEach(variable => {
      const value = variables[variable.name] || variable.defaultValue;
      if (value !== undefined) {
        // Apply variable to appropriate fields
        if (variable.name === 'content' && typeof value === 'string') {
          result.content = value;
        } else if (variable.name === 'color' && typeof value === 'string') {
          result.color = value;
        }
        // Add more variable applications as needed
      }
    });
    
    return result;
  }
  
  private exportToCSV(annotations: AdvancedAnnotation[]): string {
    const headers = ['ID', 'Type', 'Page', 'X', 'Y', 'Width', 'Height', 'Content', 'Author', 'Created'];
    const rows = annotations.map(ann => [
      ann.id,
      ann.type,
      ann.pageNumber,
      ann.x,
      ann.y,
      ann.width || '',
      ann.height || '',
      ann.content || '',
      ann.author,
      ann.createdAt.toISOString(),
    ]);
    
    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }
  
  private exportToXFDF(annotations: AdvancedAnnotation[]): string {
    // Basic XFDF export implementation
    const xfdfAnnotations = annotations.map(ann => {
      return `<${ann.type} page="${ann.pageNumber}" rect="${ann.x},${ann.y},${ann.x + (ann.width || 0)},${ann.y + (ann.height || 0)}" color="${ann.color}" contents="${ann.content || ''}" />`;
    }).join('\n');
    
    return `<?xml version="1.0" encoding="UTF-8"?>
<xfdf xmlns="http://ns.adobe.com/xfdf/" xml:space="preserve">
  <annots>
    ${xfdfAnnotations}
  </annots>
</xfdf>`;
  }
  
  private parseXFDF(xfdfData: string): AdvancedAnnotation[] {
    // Basic XFDF parsing implementation
    // In a real implementation, you'd use a proper XML parser
    const annotations: AdvancedAnnotation[] = [];
    // Simplified parsing logic would go here
    return annotations;
  }
  
  private loadDefaultTemplates(): void {
    // Load default annotation templates
    const defaultTemplates: AnnotationTemplate[] = [
      {
        id: 'approval-stamp',
        name: 'Approval Stamp',
        description: 'Standard approval stamp',
        category: 'stamps',
        thumbnail: '/templates/approval-stamp.png',
        template: {
          type: 'stamp',
          stamp: {
            type: 'predefined',
            template: 'APPROVED',
            size: 'medium',
          },
          color: '#22C55E',
        },
      },
      {
        id: 'signature-field',
        name: 'Signature Field',
        description: 'Digital signature field',
        category: 'forms',
        thumbnail: '/templates/signature-field.png',
        template: {
          type: 'signature',
          formField: {
            type: 'signature',
            name: 'signature',
            required: true,
          },
        },
      },
    ];
    
    defaultTemplates.forEach(template => {
      this.templates.set(template.id, template);
    });
  }
}
