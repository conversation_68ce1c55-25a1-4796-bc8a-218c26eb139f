import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import ErrorRecovery from '@/components/core/error-recovery'
import { EnhancedErrorH<PERSON>ler, ErrorDisplay } from '@/components/core/enhanced-error-handler'

// Mock sonner for toast notifications
vi.mock('sonner', () => ({
  toast: {
    error: vi.fn(),
    success: vi.fn(),
    info: vi.fn(),
    warning: vi.fn(),
  },
}))

// Mock the error recovery component
vi.mock('@/components/core/error-recovery', () => ({
  default: ({ error, onRetry, onReset, children }: {
    error?: Error;
    onRetry?: () => void;
    onReset?: () => void;
    children?: React.ReactNode;
  }) => {
    if (error) {
      return (
        <div data-testid="error-recovery">
          <div data-testid="error-message">{error.message}</div>
          <button data-testid="retry-button" onClick={onRetry}>
            Retry
          </button>
          <button data-testid="reset-button" onClick={onReset}>
            Reset
          </button>
        </div>
      )
    }
    return <div data-testid="error-recovery-children">{children}</div>
  },
}))

// Mock the enhanced error handler
vi.mock('@/components/core/enhanced-error-handler', () => ({
  EnhancedErrorHandler: ({ children, onError }: {
    children: React.ReactNode;
    onError?: (error: Error, errorInfo: any) => void;
  }) => {
    return (
      <div data-testid="enhanced-error-handler">
        {children}
      </div>
    )
  },
  ErrorDisplay: ({ error, onRetry, onDismiss }: {
    error: Error;
    onRetry?: () => void;
    onDismiss?: () => void;
  }) => (
    <div data-testid="error-display">
      <div data-testid="error-display-message">{error.message}</div>
      {onRetry && (
        <button data-testid="error-display-retry" onClick={onRetry}>
          Try Again
        </button>
      )}
      {onDismiss && (
        <button data-testid="error-display-dismiss" onClick={onDismiss}>
          Dismiss
        </button>
      )}
    </div>
  ),
}))

describe('Error Handling', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('ErrorRecovery Component', () => {
    it('renders children when no error occurs', () => {
      render(
        <ErrorRecovery>
          <div data-testid="normal-content">Normal content</div>
        </ErrorRecovery>
      )

      expect(screen.getByTestId('error-recovery-children')).toBeInTheDocument()
      expect(screen.getByTestId('normal-content')).toBeInTheDocument()
    })

    it('renders error UI when error occurs', () => {
      const error = new Error('Test error occurred')

      render(
        <ErrorRecovery error={error}>
          <div data-testid="normal-content">Normal content</div>
        </ErrorRecovery>
      )

      expect(screen.getByTestId('error-recovery')).toBeInTheDocument()
      expect(screen.getByTestId('error-message')).toHaveTextContent('Test error occurred')
      expect(screen.getByTestId('retry-button')).toBeInTheDocument()
      expect(screen.getByTestId('reset-button')).toBeInTheDocument()
    })

    it('calls onRetry when retry button is clicked', () => {
      const onRetry = vi.fn()
      const error = new Error('Test error')

      render(
        <ErrorRecovery error={error} onRetry={onRetry}>
          <div>Content</div>
        </ErrorRecovery>
      )

      fireEvent.click(screen.getByTestId('retry-button'))
      expect(onRetry).toHaveBeenCalledTimes(1)
    })

    it('calls onReset when reset button is clicked', () => {
      const onReset = vi.fn()
      const error = new Error('Test error')

      render(
        <ErrorRecovery error={error} onReset={onReset}>
          <div>Content</div>
        </ErrorRecovery>
      )

      fireEvent.click(screen.getByTestId('reset-button'))
      expect(onReset).toHaveBeenCalledTimes(1)
    })

    it('handles different error types appropriately', () => {
      const networkError = new Error('Network error')
      const { rerender } = render(
        <ErrorRecovery error={networkError}>
          <div>Content</div>
        </ErrorRecovery>
      )

      expect(screen.getByTestId('error-message')).toHaveTextContent('Network error')

      const parseError = new Error('Parse error')
      rerender(
        <ErrorRecovery error={parseError}>
          <div>Content</div>
        </ErrorRecovery>
      )

      expect(screen.getByTestId('error-message')).toHaveTextContent('Parse error')
    })
  })

  describe('EnhancedErrorHandler Component', () => {
    it('renders children normally when no error occurs', () => {
      render(
        <EnhancedErrorHandler>
          <div data-testid="normal-content">Normal content</div>
        </EnhancedErrorHandler>
      )

      expect(screen.getByTestId('enhanced-error-handler')).toBeInTheDocument()
      expect(screen.getByTestId('normal-content')).toBeInTheDocument()
    })

    it('provides error boundary functionality', () => {
      const onError = vi.fn()

      render(
        <EnhancedErrorHandler onError={onError}>
          <div data-testid="content">Content that might error</div>
        </EnhancedErrorHandler>
      )

      expect(screen.getByTestId('enhanced-error-handler')).toBeInTheDocument()
      expect(onError).not.toHaveBeenCalled()
    })
  })

  describe('ErrorDisplay Component', () => {
    it('displays error message correctly', () => {
      const error = new Error('Display test error')

      render(
        <ErrorDisplay error={error} />
      )

      expect(screen.getByTestId('error-display')).toBeInTheDocument()
      expect(screen.getByTestId('error-display-message')).toHaveTextContent('Display test error')
    })

    it('shows retry button when onRetry is provided', () => {
      const error = new Error('Retryable error')
      const onRetry = vi.fn()

      render(
        <ErrorDisplay error={error} onRetry={onRetry} />
      )

      expect(screen.getByTestId('error-display-retry')).toBeInTheDocument()
    })

    it('shows dismiss button when onDismiss is provided', () => {
      const error = new Error('Dismissible error')
      const onDismiss = vi.fn()

      render(
        <ErrorDisplay error={error} onDismiss={onDismiss} />
      )

      expect(screen.getByTestId('error-display-dismiss')).toBeInTheDocument()
    })

    it('calls onRetry when retry button is clicked', () => {
      const error = new Error('Retryable error')
      const onRetry = vi.fn()

      render(
        <ErrorDisplay error={error} onRetry={onRetry} />
      )

      fireEvent.click(screen.getByTestId('error-display-retry'))
      expect(onRetry).toHaveBeenCalledTimes(1)
    })

    it('calls onDismiss when dismiss button is clicked', () => {
      const error = new Error('Dismissible error')
      const onDismiss = vi.fn()

      render(
        <ErrorDisplay error={error} onDismiss={onDismiss} />
      )

      fireEvent.click(screen.getByTestId('error-display-dismiss'))
      expect(onDismiss).toHaveBeenCalledTimes(1)
    })

    it('shows both buttons when both callbacks are provided', () => {
      const error = new Error('Full featured error')
      const onRetry = vi.fn()
      const onDismiss = vi.fn()

      render(
        <ErrorDisplay error={error} onRetry={onRetry} onDismiss={onDismiss} />
      )

      expect(screen.getByTestId('error-display-retry')).toBeInTheDocument()
      expect(screen.getByTestId('error-display-dismiss')).toBeInTheDocument()
    })
  })

  describe('Error Recovery Scenarios', () => {
    it('handles PDF loading errors with recovery options', () => {
      const pdfError = new Error('Failed to load PDF document')
      const onRetry = vi.fn()
      const onReset = vi.fn()

      render(
        <ErrorRecovery error={pdfError} onRetry={onRetry} onReset={onReset}>
          <div>PDF Viewer</div>
        </ErrorRecovery>
      )

      expect(screen.getByTestId('error-message')).toHaveTextContent('Failed to load PDF document')
      
      // Test retry functionality
      fireEvent.click(screen.getByTestId('retry-button'))
      expect(onRetry).toHaveBeenCalled()

      // Test reset functionality
      fireEvent.click(screen.getByTestId('reset-button'))
      expect(onReset).toHaveBeenCalled()
    })

    it('handles network errors with appropriate messaging', () => {
      const networkError = new Error('Network request failed')

      render(
        <ErrorDisplay error={networkError} />
      )

      expect(screen.getByTestId('error-display-message')).toHaveTextContent('Network request failed')
    })

    it('handles parsing errors with user-friendly messages', () => {
      const parseError = new Error('Invalid PDF format')

      render(
        <ErrorDisplay error={parseError} />
      )

      expect(screen.getByTestId('error-display-message')).toHaveTextContent('Invalid PDF format')
    })

    it('handles permission errors appropriately', () => {
      const permissionError = new Error('Access denied')

      render(
        <ErrorDisplay error={permissionError} />
      )

      expect(screen.getByTestId('error-display-message')).toHaveTextContent('Access denied')
    })
  })

  describe('Error State Management', () => {
    it('clears error state after successful retry', async () => {
      let hasError = true
      const onRetry = vi.fn(() => {
        hasError = false
      })

      const { rerender } = render(
        <ErrorRecovery error={hasError ? new Error('Test error') : undefined} onRetry={onRetry}>
          <div data-testid="content">Content</div>
        </ErrorRecovery>
      )

      // Initially shows error
      expect(screen.getByTestId('error-recovery')).toBeInTheDocument()

      // Click retry
      fireEvent.click(screen.getByTestId('retry-button'))
      expect(onRetry).toHaveBeenCalled()

      // Rerender without error
      rerender(
        <ErrorRecovery error={undefined} onRetry={onRetry}>
          <div data-testid="content">Content</div>
        </ErrorRecovery>
      )

      // Should show normal content
      expect(screen.getByTestId('error-recovery-children')).toBeInTheDocument()
      expect(screen.getByTestId('content')).toBeInTheDocument()
    })

    it('maintains error state until explicitly cleared', () => {
      const error = new Error('Persistent error')

      const { rerender } = render(
        <ErrorRecovery error={error}>
          <div>Content</div>
        </ErrorRecovery>
      )

      expect(screen.getByTestId('error-recovery')).toBeInTheDocument()

      // Rerender with same error
      rerender(
        <ErrorRecovery error={error}>
          <div>Content</div>
        </ErrorRecovery>
      )

      // Should still show error
      expect(screen.getByTestId('error-recovery')).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('provides accessible error messages', () => {
      const error = new Error('Accessible error message')

      render(
        <ErrorDisplay error={error} />
      )

      const errorMessage = screen.getByTestId('error-display-message')
      expect(errorMessage).toBeInTheDocument()
      expect(errorMessage).toHaveTextContent('Accessible error message')
    })

    it('provides accessible retry and dismiss buttons', () => {
      const error = new Error('Accessible error')
      const onRetry = vi.fn()
      const onDismiss = vi.fn()

      render(
        <ErrorDisplay error={error} onRetry={onRetry} onDismiss={onDismiss} />
      )

      const retryButton = screen.getByTestId('error-display-retry')
      const dismissButton = screen.getByTestId('error-display-dismiss')

      expect(retryButton).toBeInTheDocument()
      expect(dismissButton).toBeInTheDocument()
      expect(retryButton).toHaveTextContent('Try Again')
      expect(dismissButton).toHaveTextContent('Dismiss')
    })
  })
})
