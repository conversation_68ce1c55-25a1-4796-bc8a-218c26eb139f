import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'

// Mock react-pdf Page component
vi.mock('react-pdf', () => ({
  Page: vi.fn(({ 
    pageNumber, 
    scale = 1, 
    rotate = 0, 
    onLoadSuccess, 
    onLoadError,
    renderTextLayer = true,
    renderAnnotationLayer = true,
    loading,
    className,
    customTextRenderer
  }) => {
    // Simulate page load success
    setTimeout(() => {
      if (onLoadSuccess) {
        onLoadSuccess({
          width: 612 * scale,
          height: 792 * scale,
          originalWidth: 612,
          originalHeight: 792,
          pageNumber,
          scale,
          rotate
        })
      }
    }, 50)

    return (
      <div 
        data-testid={`pdf-page-${pageNumber}`}
        className={className}
        style={{
          width: 612 * scale,
          height: 792 * scale,
          transform: `rotate(${rotate}deg)`
        }}
      >
        {loading && <div data-testid="page-loading">{loading}</div>}
        <div data-testid="page-content">Page {pageNumber}</div>
        {renderTextLayer && <div data-testid="text-layer">Text Layer</div>}
        {renderAnnotationLayer && <div data-testid="annotation-layer">Annotation Layer</div>}
        {customTextRenderer && <div data-testid="custom-text">Custom Text Renderer</div>}
      </div>
    )
  }),
}))

describe('PDF Page Rendering', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Basic Page Rendering', () => {
    it('renders a PDF page successfully', async () => {
      const onLoadSuccess = vi.fn()

      render(
        <Page
          pageNumber={1}
          onLoadSuccess={onLoadSuccess}
        />
      )

      expect(screen.getByTestId('pdf-page-1')).toBeInTheDocument()
      expect(screen.getByTestId('page-content')).toHaveTextContent('Page 1')

      await waitFor(() => {
        expect(onLoadSuccess).toHaveBeenCalledWith({
          width: 612,
          height: 792,
          originalWidth: 612,
          originalHeight: 792,
          pageNumber: 1,
          scale: 1,
          rotate: 0
        })
      })
    })

    it('renders multiple pages with different page numbers', async () => {
      render(
        <div>
          <Page pageNumber={1} />
          <Page pageNumber={2} />
          <Page pageNumber={3} />
        </div>
      )

      expect(screen.getByTestId('pdf-page-1')).toBeInTheDocument()
      expect(screen.getByTestId('pdf-page-2')).toBeInTheDocument()
      expect(screen.getByTestId('pdf-page-3')).toBeInTheDocument()

      expect(screen.getByTestId('pdf-page-1')).toHaveTextContent('Page 1')
      expect(screen.getByTestId('pdf-page-2')).toHaveTextContent('Page 2')
      expect(screen.getByTestId('pdf-page-3')).toHaveTextContent('Page 3')
    })

    it('applies custom className to page', () => {
      render(
        <Page
          pageNumber={1}
          className="custom-page-class"
        />
      )

      const pageElement = screen.getByTestId('pdf-page-1')
      expect(pageElement).toHaveClass('custom-page-class')
    })
  })

  describe('Page Scaling', () => {
    it('renders page at default scale (1.0)', async () => {
      const onLoadSuccess = vi.fn()

      render(
        <Page
          pageNumber={1}
          scale={1.0}
          onLoadSuccess={onLoadSuccess}
        />
      )

      const pageElement = screen.getByTestId('pdf-page-1')
      expect(pageElement).toHaveStyle({
        width: '612px',
        height: '792px'
      })

      await waitFor(() => {
        expect(onLoadSuccess).toHaveBeenCalledWith(
          expect.objectContaining({
            width: 612,
            height: 792,
            scale: 1.0
          })
        )
      })
    })

    it('renders page at 150% scale', async () => {
      const onLoadSuccess = vi.fn()

      render(
        <Page
          pageNumber={1}
          scale={1.5}
          onLoadSuccess={onLoadSuccess}
        />
      )

      const pageElement = screen.getByTestId('pdf-page-1')
      expect(pageElement).toHaveStyle({
        width: '918px', // 612 * 1.5
        height: '1188px' // 792 * 1.5
      })

      await waitFor(() => {
        expect(onLoadSuccess).toHaveBeenCalledWith(
          expect.objectContaining({
            width: 918,
            height: 1188,
            scale: 1.5
          })
        )
      })
    })

    it('renders page at 50% scale', async () => {
      const onLoadSuccess = vi.fn()

      render(
        <Page
          pageNumber={1}
          scale={0.5}
          onLoadSuccess={onLoadSuccess}
        />
      )

      const pageElement = screen.getByTestId('pdf-page-1')
      expect(pageElement).toHaveStyle({
        width: '306px', // 612 * 0.5
        height: '396px' // 792 * 0.5
      })

      await waitFor(() => {
        expect(onLoadSuccess).toHaveBeenCalledWith(
          expect.objectContaining({
            width: 306,
            height: 396,
            scale: 0.5
          })
        )
      })
    })
  })

  describe('Page Rotation', () => {
    it('renders page with no rotation (0 degrees)', () => {
      render(
        <Page
          pageNumber={1}
          rotate={0}
        />
      )

      const pageElement = screen.getByTestId('pdf-page-1')
      expect(pageElement).toHaveStyle({
        transform: 'rotate(0deg)'
      })
    })

    it('renders page rotated 90 degrees', () => {
      render(
        <Page
          pageNumber={1}
          rotate={90}
        />
      )

      const pageElement = screen.getByTestId('pdf-page-1')
      expect(pageElement).toHaveStyle({
        transform: 'rotate(90deg)'
      })
    })

    it('renders page rotated 180 degrees', () => {
      render(
        <Page
          pageNumber={1}
          rotate={180}
        />
      )

      const pageElement = screen.getByTestId('pdf-page-1')
      expect(pageElement).toHaveStyle({
        transform: 'rotate(180deg)'
      })
    })

    it('renders page rotated 270 degrees', () => {
      render(
        <Page
          pageNumber={1}
          rotate={270}
        />
      )

      const pageElement = screen.getByTestId('pdf-page-1')
      expect(pageElement).toHaveStyle({
        transform: 'rotate(270deg)'
      })
    })
  })

  describe('Layer Rendering', () => {
    it('renders text layer by default', () => {
      render(
        <Page
          pageNumber={1}
          renderTextLayer={true}
        />
      )

      expect(screen.getByTestId('text-layer')).toBeInTheDocument()
    })

    it('does not render text layer when disabled', () => {
      render(
        <Page
          pageNumber={1}
          renderTextLayer={false}
        />
      )

      expect(screen.queryByTestId('text-layer')).not.toBeInTheDocument()
    })

    it('renders annotation layer by default', () => {
      render(
        <Page
          pageNumber={1}
          renderAnnotationLayer={true}
        />
      )

      expect(screen.getByTestId('annotation-layer')).toBeInTheDocument()
    })

    it('does not render annotation layer when disabled', () => {
      render(
        <Page
          pageNumber={1}
          renderAnnotationLayer={false}
        />
      )

      expect(screen.queryByTestId('annotation-layer')).not.toBeInTheDocument()
    })

    it('renders both layers when both are enabled', () => {
      render(
        <Page
          pageNumber={1}
          renderTextLayer={true}
          renderAnnotationLayer={true}
        />
      )

      expect(screen.getByTestId('text-layer')).toBeInTheDocument()
      expect(screen.getByTestId('annotation-layer')).toBeInTheDocument()
    })

    it('renders neither layer when both are disabled', () => {
      render(
        <Page
          pageNumber={1}
          renderTextLayer={false}
          renderAnnotationLayer={false}
        />
      )

      expect(screen.queryByTestId('text-layer')).not.toBeInTheDocument()
      expect(screen.queryByTestId('annotation-layer')).not.toBeInTheDocument()
    })
  })

  describe('Custom Text Rendering', () => {
    it('uses custom text renderer when provided', () => {
      const customTextRenderer = vi.fn()

      render(
        <Page
          pageNumber={1}
          customTextRenderer={customTextRenderer}
        />
      )

      expect(screen.getByTestId('custom-text')).toBeInTheDocument()
    })

    it('does not show custom text renderer when not provided', () => {
      render(
        <Page
          pageNumber={1}
        />
      )

      expect(screen.queryByTestId('custom-text')).not.toBeInTheDocument()
    })
  })

  describe('Loading States', () => {
    it('shows loading component while page loads', () => {
      render(
        <Page
          pageNumber={1}
          loading={<div>Loading page...</div>}
        />
      )

      expect(screen.getByTestId('page-loading')).toHaveTextContent('Loading page...')
    })

    it('shows custom loading component', () => {
      const CustomLoading = () => <div data-testid="custom-page-loading">Custom Page Loading</div>

      render(
        <Page
          pageNumber={1}
          loading={<CustomLoading />}
        />
      )

      expect(screen.getByTestId('custom-page-loading')).toBeInTheDocument()
    })
  })

  describe('Error Handling', () => {
    it('handles page load errors', async () => {
      const onLoadError = vi.fn()

      // Mock Page to trigger error
      vi.mocked(Page).mockImplementationOnce(({ onLoadError: errorCallback }) => {
        setTimeout(() => {
          if (errorCallback) {
            errorCallback(new Error('Page failed to load'))
          }
        }, 50)
        return <div data-testid="page-error">Error loading page</div>
      })

      render(
        <Page
          pageNumber={1}
          onLoadError={onLoadError}
        />
      )

      await waitFor(() => {
        expect(onLoadError).toHaveBeenCalledWith(new Error('Page failed to load'))
      })
    })

    it('handles invalid page numbers gracefully', async () => {
      const onLoadError = vi.fn()

      vi.mocked(Page).mockImplementationOnce(({ onLoadError: errorCallback }) => {
        setTimeout(() => {
          if (errorCallback) {
            errorCallback(new Error('Invalid page number'))
          }
        }, 50)
        return <div data-testid="page-error">Invalid page</div>
      })

      render(
        <Page
          pageNumber={-1}
          onLoadError={onLoadError}
        />
      )

      await waitFor(() => {
        expect(onLoadError).toHaveBeenCalledWith(new Error('Invalid page number'))
      })
    })
  })

  describe('Performance Optimizations', () => {
    it('handles device pixel ratio for high-DPI displays', () => {
      // Mock window.devicePixelRatio
      Object.defineProperty(window, 'devicePixelRatio', {
        writable: true,
        value: 2,
      })

      render(
        <Page
          pageNumber={1}
          devicePixelRatio={window.devicePixelRatio}
        />
      )

      expect(screen.getByTestId('pdf-page-1')).toBeInTheDocument()
    })

    it('applies canvas background for better rendering', () => {
      render(
        <Page
          pageNumber={1}
          canvasBackground="white"
        />
      )

      expect(screen.getByTestId('pdf-page-1')).toBeInTheDocument()
    })
  })
})
