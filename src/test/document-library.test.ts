import { describe, it, expect, beforeEach, vi } from 'vitest';

// Unmock the document library for this test file
vi.unmock('../lib/document-library');

import { DocumentLibraryStorage } from '../lib/document-library';
import { createDefaultDocumentMetadata } from '../lib/types/pdf';

// Mock IndexedDB
const mockIDBObjectStore = {
  add: vi.fn().mockReturnValue({ onsuccess: null, onerror: null }),
  get: vi.fn().mockReturnValue({ onsuccess: null, onerror: null, result: null }),
  put: vi.fn().mockReturnValue({ onsuccess: null, onerror: null }),
  delete: vi.fn().mockReturnValue({ onsuccess: null, onerror: null }),
  getAll: vi.fn().mockReturnValue({ onsuccess: null, onerror: null, result: [] }),
  createIndex: vi.fn(),
};

const mockIDBTransaction = {
  objectStore: vi.fn().mockReturnValue(mockIDBObjectStore),
  oncomplete: null,
  onerror: null,
};

const mockIDBDatabase = {
  createObjectStore: vi.fn().mockReturnValue(mockIDBObjectStore),
  transaction: vi.fn().mockReturnValue(mockIDBTransaction),
  close: vi.fn(),
  objectStoreNames: {
    contains: vi.fn().mockReturnValue(false)
  }
};

const mockIDBRequest = {
  onsuccess: null,
  onerror: null,
  result: mockIDBDatabase,
  onupgradeneeded: null,
};

const mockIndexedDB = {
  open: vi.fn().mockReturnValue(mockIDBRequest),
  deleteDatabase: vi.fn(),
};

// Setup global mocks
global.indexedDB = mockIndexedDB as IDBFactory;
global.IDBDatabase = mockIDBDatabase as typeof IDBDatabase;
global.IDBTransaction = mockIDBTransaction as typeof IDBTransaction;
global.IDBObjectStore = mockIDBObjectStore as typeof IDBObjectStore;
global.IDBRequest = mockIDBRequest as typeof IDBRequest;

describe('DocumentLibraryStorage', () => {
  let documentLibrary: DocumentLibraryStorage;

  beforeEach(() => {
    documentLibrary = new DocumentLibraryStorage();
    vi.clearAllMocks();
  });

  describe('Document Metadata', () => {
    it('should create default metadata for a file', () => {
      const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
      const metadata = createDefaultDocumentMetadata(file);

      expect(metadata.title).toBe('test');
      expect(metadata.fileName).toBe('test.pdf');
      expect(metadata.fileSize).toBe(file.size);
      expect(metadata.mimeType).toBe('application/pdf');
      expect(metadata.tags).toEqual([]);
      expect(metadata.categories).toEqual([]);
      expect(metadata.isFavorite).toBe(false);
      expect(metadata.isPinned).toBe(false);
      expect(metadata.openCount).toBe(0);
    });

    it('should create default metadata for a URL', () => {
      const url = 'https://example.com/document.pdf';
      const metadata = createDefaultDocumentMetadata(url);

      expect(metadata.title).toBe('document');
      expect(metadata.fileName).toBe('document.pdf');
      expect(metadata.fileSize).toBe(0);
      expect(metadata.mimeType).toBe('application/pdf');
    });
  });

  describe('Document Storage', () => {
    it('should initialize the database', async () => {
      // Create a mock that automatically triggers success
      const mockOpenRequest = {
        onsuccess: null,
        onerror: null,
        onupgradeneeded: null,
        result: mockIDBDatabase,
      };

      mockIndexedDB.open.mockImplementation(() => {
        // Simulate async behavior
        setTimeout(() => {
          if (mockOpenRequest.onsuccess) {
            mockOpenRequest.onsuccess({ target: mockOpenRequest } as Event & { target: IDBRequest });
          }
        }, 0);
        return mockOpenRequest;
      });

      await documentLibrary.initialize();
      expect(mockIndexedDB.open).toHaveBeenCalledWith('PDFDocumentLibrary', 1);
    });

    it('should handle database initialization errors', async () => {
      const mockOpenRequest = {
        onsuccess: null,
        onerror: null,
        onupgradeneeded: null,
        result: null,
      };

      mockIndexedDB.open.mockImplementation(() => {
        // Simulate async error
        setTimeout(() => {
          if (mockOpenRequest.onerror) {
            mockOpenRequest.onerror(new Error('Database error') as Event);
          }
        }, 0);
        return mockOpenRequest;
      });

      await expect(documentLibrary.initialize()).rejects.toThrow('Failed to open IndexedDB');
    });
  });

  describe('Document Operations', () => {
    beforeEach(async () => {
      // Mock successful initialization
      const mockOpenRequest = {
        onsuccess: null,
        onerror: null,
        onupgradeneeded: null,
        result: mockIDBDatabase,
      };

      mockIndexedDB.open.mockReturnValue(mockOpenRequest);
      const initPromise = documentLibrary.initialize();
      
      if (mockOpenRequest.onsuccess) {
        mockOpenRequest.onsuccess({ target: mockOpenRequest } as Event & { target: IDBRequest });
      }
      
      await initPromise;
    });

    it('should add a document to the library', async () => {
      const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });

      // Mock transaction and store
      const mockAddRequest = {
        onsuccess: null,
        onerror: null,
        result: null
      };

      mockIDBObjectStore.add.mockImplementation(() => {
        // Simulate async success
        setTimeout(() => {
          if (mockAddRequest.onsuccess) {
            mockAddRequest.onsuccess({} as Event);
          }
        }, 0);
        return mockAddRequest;
      });

      mockIDBTransaction.objectStore.mockReturnValue(mockIDBObjectStore);
      mockIDBDatabase.transaction.mockReturnValue(mockIDBTransaction);

      const document = await documentLibrary.addDocument(file);

      expect(document.title).toBe('test');
      expect(document.file).toBe(file);
      expect(document.metadata.fileName).toBe('test.pdf');
      expect(mockIDBObjectStore.add).toHaveBeenCalled();
    });

    it('should get a document by ID', async () => {
      const documentId = 'test-doc-id';
      const mockDocument = {
        id: documentId,
        title: 'Test Document',
        file: new File(['test'], 'test.pdf'),
        metadata: createDefaultDocumentMetadata('test.pdf'),
      };

      // Mock transaction and store
      const mockGetRequest = {
        onsuccess: null,
        onerror: null,
        result: mockDocument
      };

      mockIDBObjectStore.get.mockImplementation(() => {
        // Simulate async success
        setTimeout(() => {
          if (mockGetRequest.onsuccess) {
            mockGetRequest.onsuccess({ target: mockGetRequest } as Event & { target: IDBRequest });
          }
        }, 0);
        return mockGetRequest;
      });

      mockIDBTransaction.objectStore.mockReturnValue(mockIDBObjectStore);
      mockIDBDatabase.transaction.mockReturnValue(mockIDBTransaction);

      const result = await documentLibrary.getDocument(documentId);

      expect(result).toEqual(mockDocument);
      expect(mockIDBObjectStore.get).toHaveBeenCalledWith(documentId);
    });

    it('should return null for non-existent document', async () => {
      const documentId = 'non-existent-id';

      // Mock transaction and store
      const mockGetRequest = {
        onsuccess: null,
        onerror: null,
        result: undefined
      };

      mockIDBObjectStore.get.mockImplementation(() => {
        // Simulate async success with no result
        setTimeout(() => {
          if (mockGetRequest.onsuccess) {
            mockGetRequest.onsuccess({ target: mockGetRequest } as Event & { target: IDBRequest });
          }
        }, 0);
        return mockGetRequest;
      });

      mockIDBTransaction.objectStore.mockReturnValue(mockIDBObjectStore);
      mockIDBDatabase.transaction.mockReturnValue(mockIDBTransaction);

      const result = await documentLibrary.getDocument(documentId);

      expect(result).toBeNull();
    });

    it('should update a document', async () => {
      const documentId = 'test-doc-id';
      const existingDocument = {
        id: documentId,
        title: 'Test Document',
        file: new File(['test'], 'test.pdf'),
        metadata: createDefaultDocumentMetadata('test.pdf'),
      };

      // Mock get request
      const mockGetRequest = {
        onsuccess: null,
        onerror: null,
        result: existingDocument
      };

      // Mock put request
      const mockPutRequest = {
        onsuccess: null,
        onerror: null,
        result: null
      };

      let getCallCount = 0;
      mockIDBObjectStore.get.mockImplementation(() => {
        // Simulate async success
        setTimeout(() => {
          if (mockGetRequest.onsuccess) {
            mockGetRequest.onsuccess({ target: mockGetRequest } as Event & { target: IDBRequest });
          }
        }, 0);
        return mockGetRequest;
      });

      mockIDBObjectStore.put.mockImplementation(() => {
        // Simulate async success
        setTimeout(() => {
          if (mockPutRequest.onsuccess) {
            mockPutRequest.onsuccess({ target: mockPutRequest } as Event & { target: IDBRequest });
          }
        }, 0);
        return mockPutRequest;
      });

      mockIDBTransaction.objectStore.mockReturnValue(mockIDBObjectStore);
      mockIDBDatabase.transaction.mockReturnValue(mockIDBTransaction);

      await documentLibrary.updateDocument(documentId, {
        title: 'Updated Title'
      });

      expect(mockIDBObjectStore.get).toHaveBeenCalledWith(documentId);
      expect(mockIDBObjectStore.put).toHaveBeenCalled();
    });

    it('should remove a document', async () => {
      const documentId = 'test-doc-id';

      // Mock transaction and store
      const mockDeleteRequest = {
        onsuccess: null,
        onerror: null,
        result: null
      };

      mockIDBObjectStore.delete.mockImplementation(() => {
        // Simulate async success
        setTimeout(() => {
          if (mockDeleteRequest.onsuccess) {
            mockDeleteRequest.onsuccess({ target: mockDeleteRequest } as Event & { target: IDBRequest });
          }
        }, 0);
        return mockDeleteRequest;
      });

      mockIDBTransaction.objectStore.mockReturnValue(mockIDBObjectStore);
      mockIDBDatabase.transaction.mockReturnValue(mockIDBTransaction);

      await documentLibrary.removeDocument(documentId);

      expect(mockIDBObjectStore.delete).toHaveBeenCalledWith(documentId);
    });
  });

  describe('Search Functionality', () => {
    beforeEach(async () => {
      // Mock successful initialization for search tests
      const mockOpenRequest = {
        onsuccess: null,
        onerror: null,
        onupgradeneeded: null,
        result: mockIDBDatabase,
      };

      mockIndexedDB.open.mockReturnValue(mockOpenRequest);
      const initPromise = documentLibrary.initialize();

      if (mockOpenRequest.onsuccess) {
        mockOpenRequest.onsuccess({ target: mockOpenRequest } as Event & { target: IDBRequest });
      }

      await initPromise;
    });

    it('should search documents by text', async () => {
      const documents = [
        {
          id: '1',
          title: 'JavaScript Guide',
          metadata: {
            title: 'JavaScript Guide',
            author: 'John Doe',
            tags: ['programming', 'javascript'],
            categories: ['tutorials'],
            collections: [],
            keywords: [],
          },
        },
        {
          id: '2',
          title: 'Python Basics',
          metadata: {
            title: 'Python Basics',
            author: 'Jane Smith',
            tags: ['programming', 'python'],
            categories: ['tutorials'],
            collections: [],
            keywords: [],
          },
        },
      ];

      // Mock getAllDocuments which is called by searchDocuments
      const mockGetAllRequest = {
        onsuccess: null,
        onerror: null,
        result: documents
      };

      mockIDBObjectStore.getAll.mockImplementation(() => {
        // Simulate async success
        setTimeout(() => {
          if (mockGetAllRequest.onsuccess) {
            mockGetAllRequest.onsuccess({ target: mockGetAllRequest } as Event & { target: IDBRequest });
          }
        }, 0);
        return mockGetAllRequest;
      });

      mockIDBTransaction.objectStore.mockReturnValue(mockIDBObjectStore);
      mockIDBDatabase.transaction.mockReturnValue(mockIDBTransaction);

      const result = await documentLibrary.searchDocuments({ text: 'javascript' });

      expect(result.documents).toHaveLength(1);
      expect(result.documents[0].title).toBe('JavaScript Guide');
      expect(result.totalCount).toBe(1);
    });
  });
});
