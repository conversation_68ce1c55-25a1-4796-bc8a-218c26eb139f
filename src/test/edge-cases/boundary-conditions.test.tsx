import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import React from 'react'

// Mock components for boundary condition testing
const MockPDFViewer = ({
  pageNumber = 1,
  numPages = 10,
  scale = 1.0,
  onPageChange,
  onScaleChange,
  onError
}: {
  pageNumber?: number;
  numPages?: number;
  scale?: number;
  onPageChange?: (page: number) => void;
  onScaleChange?: (scale: number) => void;
  onError?: (error: Error) => void;
}) => {
  const handlePageChange = (newPage: number) => {
    if (newPage < 1 || newPage > numPages) {
      onError?.(new Error(`Page ${newPage} is out of range (1-${numPages})`))
      return
    }
    onPageChange?.(newPage)
  }

  const handleScaleChange = (newScale: number) => {
    if (newScale < 0.1 || newScale > 5.0) {
      onError?.(new Error(`Scale ${newScale} is out of range (0.1-5.0)`))
      return
    }
    onScaleChange?.(newScale)
  }

  return (
    <div data-testid="pdf-viewer">
      <div data-testid="page-info">Page {pageNumber} of {numPages}</div>
      <div data-testid="scale-info">Scale: {Math.round(scale * 100)}%</div>
      
      <button
        data-testid="first-page"
        onClick={() => handlePageChange(1)}
        disabled={pageNumber === 1 || numPages === 0}
      >
        First
      </button>

      <button
        data-testid="prev-page"
        onClick={() => handlePageChange(pageNumber - 1)}
        disabled={pageNumber <= 1 || numPages === 0}
      >
        Previous
      </button>
      
      <input
        data-testid="page-input"
        type="number"
        min="1"
        max={numPages}
        value={pageNumber}
        onChange={(e) => handlePageChange(parseInt(e.target.value) || 1)}
      />
      
      <button
        data-testid="next-page"
        onClick={() => handlePageChange(pageNumber + 1)}
        disabled={pageNumber >= numPages || numPages === 0}
      >
        Next
      </button>

      <button
        data-testid="last-page"
        onClick={() => handlePageChange(numPages)}
        disabled={pageNumber === numPages || numPages === 0}
      >
        Last
      </button>

      <button
        data-testid="zoom-out"
        onClick={() => handleScaleChange(scale * 0.8)}
      >
        Zoom Out
      </button>
      
      <button
        data-testid="zoom-in"
        onClick={() => handleScaleChange(scale * 1.25)}
      >
        Zoom In
      </button>
      
      <button
        data-testid="zoom-fit"
        onClick={() => handleScaleChange(1.0)}
      >
        Fit
      </button>
    </div>
  )
}

const MockPDFSearch = ({
  searchText: initialSearchText = "",
  onSearchChange,
  maxResults = 100
}: {
  searchText?: string;
  onSearchChange?: (text: string) => void;
  maxResults?: number;
}) => {
  const [searchText, setSearchText] = React.useState(initialSearchText)

  const handleSearchChange = (text: string) => {
    // Truncate text to max length
    const truncatedText = text.slice(0, 1000)
    setSearchText(truncatedText)
    onSearchChange?.(truncatedText)
  }

  return (
    <div data-testid="pdf-search">
      <input
        data-testid="search-input"
        type="text"
        value={searchText}
        onChange={(e) => handleSearchChange(e.target.value)}
        maxLength={1000}
      />
      <div data-testid="search-info">
        {searchText.length}/1000 characters
      </div>
    </div>
  )
}

describe('Boundary Conditions and Edge Cases', () => {
  let mockCallbacks: {
    onPageChange: ReturnType<typeof vi.fn>;
    onScaleChange: ReturnType<typeof vi.fn>;
    onError: ReturnType<typeof vi.fn>;
    onSearchChange: ReturnType<typeof vi.fn>;
  }

  beforeEach(() => {
    vi.clearAllMocks()
    mockCallbacks = {
      onPageChange: vi.fn(),
      onScaleChange: vi.fn(),
      onError: vi.fn(),
      onSearchChange: vi.fn(),
    }
  })

  describe('Page Navigation Boundaries', () => {
    it('handles navigation to first page when already on first page', async () => {
      const user = userEvent.setup()

      render(
        <MockPDFViewer
          pageNumber={1}
          numPages={10}
          onPageChange={mockCallbacks.onPageChange}
          onError={mockCallbacks.onError}
        />
      )

      const firstPageBtn = screen.getByTestId('first-page')
      const prevPageBtn = screen.getByTestId('prev-page')

      expect(firstPageBtn).toBeDisabled()
      expect(prevPageBtn).toBeDisabled()

      await user.click(firstPageBtn)
      expect(mockCallbacks.onPageChange).not.toHaveBeenCalled()
    })

    it('handles navigation to last page when already on last page', async () => {
      const user = userEvent.setup()

      render(
        <MockPDFViewer
          pageNumber={10}
          numPages={10}
          onPageChange={mockCallbacks.onPageChange}
          onError={mockCallbacks.onError}
        />
      )

      const lastPageBtn = screen.getByTestId('last-page')
      const nextPageBtn = screen.getByTestId('next-page')

      expect(lastPageBtn).toBeDisabled()
      expect(nextPageBtn).toBeDisabled()

      await user.click(lastPageBtn)
      expect(mockCallbacks.onPageChange).not.toHaveBeenCalled()
    })

    it('handles invalid page numbers in input', async () => {
      const user = userEvent.setup()

      render(
        <MockPDFViewer
          pageNumber={5}
          numPages={10}
          onPageChange={mockCallbacks.onPageChange}
          onError={mockCallbacks.onError}
        />
      )

      const pageInput = screen.getByTestId('page-input')

      // Test negative page number
      await user.clear(pageInput)
      await user.type(pageInput, '-1')
      fireEvent.change(pageInput, { target: { value: '-1' } })

      expect(mockCallbacks.onError).toHaveBeenCalledWith(
        expect.objectContaining({
          message: expect.stringContaining('out of range')
        })
      )

      // Test page number too high
      await user.clear(pageInput)
      await user.type(pageInput, '15')
      fireEvent.change(pageInput, { target: { value: '15' } })

      expect(mockCallbacks.onError).toHaveBeenCalledWith(
        expect.objectContaining({
          message: expect.stringContaining('out of range')
        })
      )
    })

    it('handles zero page number', async () => {
      const user = userEvent.setup()

      render(
        <MockPDFViewer
          pageNumber={5}
          numPages={10}
          onPageChange={mockCallbacks.onPageChange}
          onError={mockCallbacks.onError}
        />
      )

      const pageInput = screen.getByTestId('page-input')

      await user.clear(pageInput)
      await user.type(pageInput, '0')
      fireEvent.change(pageInput, { target: { value: '0' } })

      expect(mockCallbacks.onError).toHaveBeenCalledWith(
        expect.objectContaining({
          message: expect.stringContaining('out of range')
        })
      )
    })

    it('handles non-numeric page input', async () => {
      const user = userEvent.setup()

      render(
        <MockPDFViewer
          pageNumber={5}
          numPages={10}
          onPageChange={mockCallbacks.onPageChange}
          onError={mockCallbacks.onError}
        />
      )

      const pageInput = screen.getByTestId('page-input')

      await user.clear(pageInput)
      await user.type(pageInput, 'abc')
      fireEvent.change(pageInput, { target: { value: 'abc' } })

      // Should default to page 1 for invalid input
      expect(mockCallbacks.onPageChange).toHaveBeenCalledWith(1)
    })

    it('handles empty page input', async () => {
      const user = userEvent.setup()

      render(
        <MockPDFViewer
          pageNumber={5}
          numPages={10}
          onPageChange={mockCallbacks.onPageChange}
          onError={mockCallbacks.onError}
        />
      )

      const pageInput = screen.getByTestId('page-input')

      await user.clear(pageInput)
      fireEvent.change(pageInput, { target: { value: '' } })

      // Should default to page 1 for empty input
      expect(mockCallbacks.onPageChange).toHaveBeenCalledWith(1)
    })
  })

  describe('Zoom Scale Boundaries', () => {
    it('handles minimum zoom scale', async () => {
      const user = userEvent.setup()

      render(
        <MockPDFViewer
          scale={0.1}
          onScaleChange={mockCallbacks.onScaleChange}
          onError={mockCallbacks.onError}
        />
      )

      const zoomOutBtn = screen.getByTestId('zoom-out')

      await user.click(zoomOutBtn)

      expect(mockCallbacks.onError).toHaveBeenCalledWith(
        expect.objectContaining({
          message: expect.stringContaining('out of range')
        })
      )
    })

    it('handles maximum zoom scale', async () => {
      const user = userEvent.setup()

      render(
        <MockPDFViewer
          scale={5.0}
          onScaleChange={mockCallbacks.onScaleChange}
          onError={mockCallbacks.onError}
        />
      )

      const zoomInBtn = screen.getByTestId('zoom-in')

      await user.click(zoomInBtn)

      expect(mockCallbacks.onError).toHaveBeenCalledWith(
        expect.objectContaining({
          message: expect.stringContaining('out of range')
        })
      )
    })

    it('handles zoom scale precision', async () => {
      const user = userEvent.setup()

      render(
        <MockPDFViewer
          scale={0.123456789}
          onScaleChange={mockCallbacks.onScaleChange}
          onError={mockCallbacks.onError}
        />
      )

      // Scale should be displayed with reasonable precision
      expect(screen.getByTestId('scale-info')).toHaveTextContent('Scale: 12%')
    })
  })

  describe('Single Page Document Edge Cases', () => {
    it('handles single page document navigation', async () => {
      const user = userEvent.setup()

      render(
        <MockPDFViewer
          pageNumber={1}
          numPages={1}
          onPageChange={mockCallbacks.onPageChange}
          onError={mockCallbacks.onError}
        />
      )

      // All navigation buttons should be disabled
      expect(screen.getByTestId('first-page')).toBeDisabled()
      expect(screen.getByTestId('prev-page')).toBeDisabled()
      expect(screen.getByTestId('next-page')).toBeDisabled()
      expect(screen.getByTestId('last-page')).toBeDisabled()

      // Page input should still work
      const pageInput = screen.getByTestId('page-input')
      expect(pageInput).toHaveValue(1)
      expect(pageInput).toHaveAttribute('max', '1')
    })
  })

  describe('Empty Document Edge Cases', () => {
    it('handles zero page document', () => {
      render(
        <MockPDFViewer
          pageNumber={0}
          numPages={0}
          onPageChange={mockCallbacks.onPageChange}
          onError={mockCallbacks.onError}
        />
      )

      expect(screen.getByTestId('page-info')).toHaveTextContent('Page 0 of 0')

      // All navigation should be disabled
      expect(screen.getByTestId('first-page')).toBeDisabled()
      expect(screen.getByTestId('prev-page')).toBeDisabled()
      expect(screen.getByTestId('next-page')).toBeDisabled()
      expect(screen.getByTestId('last-page')).toBeDisabled()
    })
  })

  describe('Search Input Boundaries', () => {
    it('handles maximum search text length', () => {
      render(
        <MockPDFSearch
          onSearchChange={mockCallbacks.onSearchChange}
        />
      )

      const searchInput = screen.getByTestId('search-input')

      // Use fireEvent.change to set text up to the limit
      const longText = 'a'.repeat(1000)
      fireEvent.change(searchInput, { target: { value: longText } })

      expect(mockCallbacks.onSearchChange).toHaveBeenCalledWith(longText)
      expect(screen.getByTestId('search-info')).toHaveTextContent('1000/1000 characters')
    })

    it('handles search text exceeding maximum length', () => {
      const onSearchChange = vi.fn((text: string) => {
        if (text.length > 1000) {
          throw new Error('Search text too long')
        }
      })

      render(
        <MockPDFSearch onSearchChange={onSearchChange} />
      )

      const searchInput = screen.getByTestId('search-input')

      // Try to input text longer than limit - should be prevented by maxLength
      const tooLongText = 'a'.repeat(1001)

      // The input has maxLength=1000, so it should truncate the input
      fireEvent.change(searchInput, { target: { value: tooLongText } })

      // The actual value should be truncated to 1000 characters
      expect(searchInput).toHaveValue('a'.repeat(1000))
    })

    it('handles empty search text', async () => {
      const user = userEvent.setup()

      render(
        <MockPDFSearch
          searchText="initial text"
          onSearchChange={mockCallbacks.onSearchChange}
        />
      )

      const searchInput = screen.getByTestId('search-input')

      await user.clear(searchInput)

      expect(mockCallbacks.onSearchChange).toHaveBeenCalledWith('')
    })

    it('handles special characters in search', () => {
      render(
        <MockPDFSearch onSearchChange={mockCallbacks.onSearchChange} />
      )

      const searchInput = screen.getByTestId('search-input')

      const specialChars = '!@#$%^&*()[]{}|\\:";\'<>?,./'
      fireEvent.change(searchInput, { target: { value: specialChars } })

      expect(mockCallbacks.onSearchChange).toHaveBeenCalledWith(specialChars)
    })

    it('handles unicode characters in search', () => {
      render(
        <MockPDFSearch onSearchChange={mockCallbacks.onSearchChange} />
      )

      const searchInput = screen.getByTestId('search-input')

      const unicodeText = '🔍 Search 中文 العربية'
      fireEvent.change(searchInput, { target: { value: unicodeText } })

      expect(mockCallbacks.onSearchChange).toHaveBeenCalledWith(unicodeText)
    })
  })

  describe('Rapid User Interactions', () => {
    it('handles rapid page navigation clicks', async () => {
      const user = userEvent.setup()

      render(
        <MockPDFViewer
          pageNumber={5}
          numPages={10}
          onPageChange={mockCallbacks.onPageChange}
          onError={mockCallbacks.onError}
        />
      )

      const nextPageBtn = screen.getByTestId('next-page')

      // Rapid clicks
      await user.click(nextPageBtn)
      await user.click(nextPageBtn)
      await user.click(nextPageBtn)

      expect(mockCallbacks.onPageChange).toHaveBeenCalledTimes(3)
    })

    it('handles rapid zoom changes', async () => {
      const user = userEvent.setup()

      render(
        <MockPDFViewer
          scale={1.0}
          onScaleChange={mockCallbacks.onScaleChange}
          onError={mockCallbacks.onError}
        />
      )

      const zoomInBtn = screen.getByTestId('zoom-in')

      // Rapid zoom clicks
      await user.click(zoomInBtn)
      await user.click(zoomInBtn)
      await user.click(zoomInBtn)

      expect(mockCallbacks.onScaleChange).toHaveBeenCalledTimes(3)
    })
  })

  describe('State Consistency', () => {
    it('maintains consistent state during boundary operations', async () => {
      const user = userEvent.setup()

      const { rerender } = render(
        <MockPDFViewer
          pageNumber={1}
          numPages={10}
          onPageChange={mockCallbacks.onPageChange}
          onError={mockCallbacks.onError}
        />
      )

      // Try to go to previous page from first page
      const prevPageBtn = screen.getByTestId('prev-page')
      await user.click(prevPageBtn)

      // State should remain consistent
      expect(screen.getByTestId('page-info')).toHaveTextContent('Page 1 of 10')

      // Rerender with last page
      rerender(
        <MockPDFViewer
          pageNumber={10}
          numPages={10}
          onPageChange={mockCallbacks.onPageChange}
          onError={mockCallbacks.onError}
        />
      )

      // Try to go to next page from last page
      const nextPageBtn = screen.getByTestId('next-page')
      await user.click(nextPageBtn)

      // State should remain consistent
      expect(screen.getByTestId('page-info')).toHaveTextContent('Page 10 of 10')
    })
  })
})
