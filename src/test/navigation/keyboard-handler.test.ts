import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { KeyboardNavigationHandler, type NavigationCallbacks } from '@/lib/navigation/keyboard-handler';

// Mock prompt
global.prompt = vi.fn();

describe('KeyboardNavigationHandler', () => {
  let keyboardHandler: KeyboardNavigationHandler;
  let mockCallbacks: NavigationCallbacks;

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockCallbacks = {
      onNextPage: vi.fn(),
      onPreviousPage: vi.fn(),
      onFirstPage: vi.fn(),
      onLastPage: vi.fn(),
      onZoomIn: vi.fn(),
      onZoomOut: vi.fn(),
      onZoomReset: vi.fn(),
      onZoomFit: vi.fn(),
      onZoomFitWidth: vi.fn(),
      onRotateClockwise: vi.fn(),
      onRotateCounterClockwise: vi.fn(),
      onToggleFullscreen: vi.fn(),
      onToggleSidebar: vi.fn(),
      onToggleToolbar: vi.fn(),
      onSearch: vi.fn(),
      onPrint: vi.fn(),
      onDownload: vi.fn(),
      onGoToPage: vi.fn(),
      onFocusSearch: vi.fn(),
      onEscape: vi.fn(),
    };

    keyboardHandler = new KeyboardNavigationHandler(mockCallbacks);
  });

  afterEach(() => {
    keyboardHandler.destroy();
  });

  describe('Initialization', () => {
    it('should initialize with default config', () => {
      const config = keyboardHandler.getConfig();
      expect(config.enableGlobalShortcuts).toBe(true);
      expect(config.enableNavigationShortcuts).toBe(true);
      expect(config.enableZoomShortcuts).toBe(true);
      expect(config.preventDefaultBehavior).toBe(true);
    });

    it('should initialize with custom config', () => {
      const customHandler = new KeyboardNavigationHandler(mockCallbacks, {
        enableGlobalShortcuts: false,
        enableZoomShortcuts: false,
      });
      
      const config = customHandler.getConfig();
      expect(config.enableGlobalShortcuts).toBe(false);
      expect(config.enableZoomShortcuts).toBe(false);
      
      customHandler.destroy();
    });

    it('should setup default shortcuts', () => {
      const shortcuts = keyboardHandler.getShortcuts();
      expect(shortcuts.length).toBeGreaterThan(0);
      
      // Check for essential shortcuts
      const navigationShortcuts = keyboardHandler.getShortcutsByCategory('navigation');
      const zoomShortcuts = keyboardHandler.getShortcutsByCategory('zoom');
      const generalShortcuts = keyboardHandler.getShortcutsByCategory('general');
      
      expect(navigationShortcuts.length).toBeGreaterThan(0);
      expect(zoomShortcuts.length).toBeGreaterThan(0);
      expect(generalShortcuts.length).toBeGreaterThan(0);
    });
  });

  describe('Navigation Shortcuts', () => {
    it('should handle arrow key navigation', () => {
      // Create PDF viewer container
      const pdfViewer = document.createElement('div');
      pdfViewer.setAttribute('data-pdf-viewer', 'true');
      document.body.appendChild(pdfViewer);
      pdfViewer.focus();

      // Test right arrow
      const rightArrowEvent = new KeyboardEvent('keydown', { key: 'ArrowRight' });
      Object.defineProperty(rightArrowEvent, 'target', { value: pdfViewer });
      document.dispatchEvent(rightArrowEvent);
      expect(mockCallbacks.onNextPage).toHaveBeenCalled();

      // Test left arrow
      const leftArrowEvent = new KeyboardEvent('keydown', { key: 'ArrowLeft' });
      Object.defineProperty(leftArrowEvent, 'target', { value: pdfViewer });
      document.dispatchEvent(leftArrowEvent);
      expect(mockCallbacks.onPreviousPage).toHaveBeenCalled();

      // Cleanup
      document.body.removeChild(pdfViewer);
    });

    it('should handle Home and End keys', () => {
      const pdfViewer = document.createElement('div');
      pdfViewer.setAttribute('data-pdf-viewer', 'true');
      document.body.appendChild(pdfViewer);

      // Test Home key
      const homeEvent = new KeyboardEvent('keydown', { key: 'Home' });
      Object.defineProperty(homeEvent, 'target', { value: pdfViewer });
      document.dispatchEvent(homeEvent);
      expect(mockCallbacks.onFirstPage).toHaveBeenCalled();

      // Test End key
      const endEvent = new KeyboardEvent('keydown', { key: 'End' });
      Object.defineProperty(endEvent, 'target', { value: pdfViewer });
      document.dispatchEvent(endEvent);
      expect(mockCallbacks.onLastPage).toHaveBeenCalled();

      document.body.removeChild(pdfViewer);
    });

    it('should handle space bar navigation', () => {
      const pdfViewer = document.createElement('div');
      pdfViewer.setAttribute('data-pdf-viewer', 'true');
      document.body.appendChild(pdfViewer);

      // Test space bar (next page)
      const spaceEvent = new KeyboardEvent('keydown', { key: ' ' });
      Object.defineProperty(spaceEvent, 'target', { value: pdfViewer });
      document.dispatchEvent(spaceEvent);
      expect(mockCallbacks.onNextPage).toHaveBeenCalled();

      // Test shift + space (previous page)
      const shiftSpaceEvent = new KeyboardEvent('keydown', { key: ' ', shiftKey: true });
      Object.defineProperty(shiftSpaceEvent, 'target', { value: pdfViewer });
      document.dispatchEvent(shiftSpaceEvent);
      expect(mockCallbacks.onPreviousPage).toHaveBeenCalled();

      document.body.removeChild(pdfViewer);
    });
  });

  describe('Zoom Shortcuts', () => {
    it('should handle zoom in shortcuts', () => {
      const pdfViewer = document.createElement('div');
      pdfViewer.setAttribute('data-pdf-viewer', 'true');
      document.body.appendChild(pdfViewer);

      // Test Ctrl + =
      const zoomInEvent = new KeyboardEvent('keydown', { key: '=', ctrlKey: true });
      Object.defineProperty(zoomInEvent, 'target', { value: pdfViewer });
      document.dispatchEvent(zoomInEvent);
      expect(mockCallbacks.onZoomIn).toHaveBeenCalled();

      // Test Ctrl + +
      const zoomInPlusEvent = new KeyboardEvent('keydown', { key: '+', ctrlKey: true });
      Object.defineProperty(zoomInPlusEvent, 'target', { value: pdfViewer });
      document.dispatchEvent(zoomInPlusEvent);
      expect(mockCallbacks.onZoomIn).toHaveBeenCalledTimes(2);

      document.body.removeChild(pdfViewer);
    });

    it('should handle zoom out shortcuts', () => {
      const pdfViewer = document.createElement('div');
      pdfViewer.setAttribute('data-pdf-viewer', 'true');
      document.body.appendChild(pdfViewer);

      // Test Ctrl + -
      const zoomOutEvent = new KeyboardEvent('keydown', { key: '-', ctrlKey: true });
      Object.defineProperty(zoomOutEvent, 'target', { value: pdfViewer });
      document.dispatchEvent(zoomOutEvent);
      expect(mockCallbacks.onZoomOut).toHaveBeenCalled();

      document.body.removeChild(pdfViewer);
    });

    it('should handle zoom reset and fit shortcuts', () => {
      const pdfViewer = document.createElement('div');
      pdfViewer.setAttribute('data-pdf-viewer', 'true');
      document.body.appendChild(pdfViewer);

      // Test Ctrl + 0 (reset)
      const resetEvent = new KeyboardEvent('keydown', { key: '0', ctrlKey: true });
      Object.defineProperty(resetEvent, 'target', { value: pdfViewer });
      document.dispatchEvent(resetEvent);
      expect(mockCallbacks.onZoomReset).toHaveBeenCalled();

      // Test Ctrl + 1 (fit to page)
      const fitEvent = new KeyboardEvent('keydown', { key: '1', ctrlKey: true });
      Object.defineProperty(fitEvent, 'target', { value: pdfViewer });
      document.dispatchEvent(fitEvent);
      expect(mockCallbacks.onZoomFit).toHaveBeenCalled();

      // Test Ctrl + 2 (fit to width)
      const fitWidthEvent = new KeyboardEvent('keydown', { key: '2', ctrlKey: true });
      Object.defineProperty(fitWidthEvent, 'target', { value: pdfViewer });
      document.dispatchEvent(fitWidthEvent);
      expect(mockCallbacks.onZoomFitWidth).toHaveBeenCalled();

      document.body.removeChild(pdfViewer);
    });
  });

  describe('General Shortcuts', () => {
    it('should handle search shortcuts', () => {
      const pdfViewer = document.createElement('div');
      pdfViewer.setAttribute('data-pdf-viewer', 'true');
      document.body.appendChild(pdfViewer);

      // Test Ctrl + F
      const searchEvent = new KeyboardEvent('keydown', { key: 'f', ctrlKey: true });
      Object.defineProperty(searchEvent, 'target', { value: pdfViewer });
      document.dispatchEvent(searchEvent);
      expect(mockCallbacks.onSearch).toHaveBeenCalled();

      // Test / key
      const slashEvent = new KeyboardEvent('keydown', { key: '/' });
      Object.defineProperty(slashEvent, 'target', { value: pdfViewer });
      document.dispatchEvent(slashEvent);
      expect(mockCallbacks.onFocusSearch).toHaveBeenCalled();

      document.body.removeChild(pdfViewer);
    });

    it('should handle print and download shortcuts', () => {
      const pdfViewer = document.createElement('div');
      pdfViewer.setAttribute('data-pdf-viewer', 'true');
      document.body.appendChild(pdfViewer);

      // Test Ctrl + P
      const printEvent = new KeyboardEvent('keydown', { key: 'p', ctrlKey: true });
      Object.defineProperty(printEvent, 'target', { value: pdfViewer });
      document.dispatchEvent(printEvent);
      expect(mockCallbacks.onPrint).toHaveBeenCalled();

      // Test Ctrl + S
      const downloadEvent = new KeyboardEvent('keydown', { key: 's', ctrlKey: true });
      Object.defineProperty(downloadEvent, 'target', { value: pdfViewer });
      document.dispatchEvent(downloadEvent);
      expect(mockCallbacks.onDownload).toHaveBeenCalled();

      document.body.removeChild(pdfViewer);
    });

    it('should handle escape key', () => {
      const pdfViewer = document.createElement('div');
      pdfViewer.setAttribute('data-pdf-viewer', 'true');
      document.body.appendChild(pdfViewer);

      const escapeEvent = new KeyboardEvent('keydown', { key: 'Escape' });
      Object.defineProperty(escapeEvent, 'target', { value: pdfViewer });
      document.dispatchEvent(escapeEvent);
      expect(mockCallbacks.onEscape).toHaveBeenCalled();

      document.body.removeChild(pdfViewer);
    });
  });

  describe('Go To Page Functionality', () => {
    it('should handle go to page shortcut', () => {
      (global.prompt as any).mockReturnValue('5');
      
      const pdfViewer = document.createElement('div');
      pdfViewer.setAttribute('data-pdf-viewer', 'true');
      document.body.appendChild(pdfViewer);

      const goToPageEvent = new KeyboardEvent('keydown', { key: 'g', ctrlKey: true });
      Object.defineProperty(goToPageEvent, 'target', { value: pdfViewer });
      document.dispatchEvent(goToPageEvent);

      expect(global.prompt).toHaveBeenCalledWith('Go to page:');
      expect(mockCallbacks.onGoToPage).toHaveBeenCalledWith(5);

      document.body.removeChild(pdfViewer);
    });

    it('should handle invalid page input', () => {
      (global.prompt as any).mockReturnValue('invalid');
      
      const pdfViewer = document.createElement('div');
      pdfViewer.setAttribute('data-pdf-viewer', 'true');
      document.body.appendChild(pdfViewer);

      const goToPageEvent = new KeyboardEvent('keydown', { key: 'g', ctrlKey: true });
      Object.defineProperty(goToPageEvent, 'target', { value: pdfViewer });
      document.dispatchEvent(goToPageEvent);

      expect(mockCallbacks.onGoToPage).not.toHaveBeenCalled();

      document.body.removeChild(pdfViewer);
    });

    it('should handle direct page number shortcuts', () => {
      const pdfViewer = document.createElement('div');
      pdfViewer.setAttribute('data-pdf-viewer', 'true');
      document.body.appendChild(pdfViewer);

      // Test Ctrl + 3 (go to page 3)
      const page3Event = new KeyboardEvent('keydown', { key: '3', ctrlKey: true });
      Object.defineProperty(page3Event, 'target', { value: pdfViewer });
      document.dispatchEvent(page3Event);
      expect(mockCallbacks.onGoToPage).toHaveBeenCalledWith(3);

      document.body.removeChild(pdfViewer);
    });
  });

  describe('Input Element Handling', () => {
    it('should ignore shortcuts in input elements', () => {
      const input = document.createElement('input');
      document.body.appendChild(input);

      const arrowEvent = new KeyboardEvent('keydown', { key: 'ArrowRight' });
      Object.defineProperty(arrowEvent, 'target', { value: input });
      document.dispatchEvent(arrowEvent);

      expect(mockCallbacks.onNextPage).not.toHaveBeenCalled();

      document.body.removeChild(input);
    });

    it('should allow certain shortcuts in input elements', () => {
      const input = document.createElement('input');
      document.body.appendChild(input);

      // Escape should work in input elements
      const escapeEvent = new KeyboardEvent('keydown', { key: 'Escape' });
      Object.defineProperty(escapeEvent, 'target', { value: input });
      document.dispatchEvent(escapeEvent);
      expect(mockCallbacks.onEscape).toHaveBeenCalled();

      // Ctrl + F should work in input elements
      const searchEvent = new KeyboardEvent('keydown', { key: 'f', ctrlKey: true });
      Object.defineProperty(searchEvent, 'target', { value: input });
      document.dispatchEvent(searchEvent);
      expect(mockCallbacks.onSearch).toHaveBeenCalled();

      document.body.removeChild(input);
    });
  });

  describe('Configuration Management', () => {
    it('should respect category enable/disable settings', () => {
      keyboardHandler.updateConfig({ enableNavigationShortcuts: false });
      
      const pdfViewer = document.createElement('div');
      pdfViewer.setAttribute('data-pdf-viewer', 'true');
      document.body.appendChild(pdfViewer);

      const arrowEvent = new KeyboardEvent('keydown', { key: 'ArrowRight' });
      Object.defineProperty(arrowEvent, 'target', { value: pdfViewer });
      document.dispatchEvent(arrowEvent);

      expect(mockCallbacks.onNextPage).not.toHaveBeenCalled();

      document.body.removeChild(pdfViewer);
    });

    it('should update callbacks', () => {
      const newCallbacks = {
        onNextPage: vi.fn(),
      };

      keyboardHandler.updateCallbacks(newCallbacks);

      const pdfViewer = document.createElement('div');
      pdfViewer.setAttribute('data-pdf-viewer', 'true');
      document.body.appendChild(pdfViewer);

      const arrowEvent = new KeyboardEvent('keydown', { key: 'ArrowRight' });
      Object.defineProperty(arrowEvent, 'target', { value: pdfViewer });
      document.dispatchEvent(arrowEvent);

      expect(newCallbacks.onNextPage).toHaveBeenCalled();
      expect(mockCallbacks.onNextPage).not.toHaveBeenCalled();

      document.body.removeChild(pdfViewer);
    });
  });

  describe('Custom Shortcuts', () => {
    it('should add custom shortcuts', () => {
      const customAction = vi.fn();
      
      keyboardHandler.addShortcut({
        key: 'x',
        ctrlKey: true,
        description: 'Custom action',
        category: 'general',
        action: customAction,
      });

      const pdfViewer = document.createElement('div');
      pdfViewer.setAttribute('data-pdf-viewer', 'true');
      document.body.appendChild(pdfViewer);

      const customEvent = new KeyboardEvent('keydown', { key: 'x', ctrlKey: true });
      Object.defineProperty(customEvent, 'target', { value: pdfViewer });
      document.dispatchEvent(customEvent);

      expect(customAction).toHaveBeenCalled();

      document.body.removeChild(pdfViewer);
    });

    it('should remove shortcuts', () => {
      const customShortcut = {
        key: 'y',
        ctrlKey: true,
        description: 'Custom action',
        category: 'general' as const,
        action: vi.fn(),
      };

      keyboardHandler.addShortcut(customShortcut);
      keyboardHandler.removeShortcut(customShortcut);

      const pdfViewer = document.createElement('div');
      pdfViewer.setAttribute('data-pdf-viewer', 'true');
      document.body.appendChild(pdfViewer);

      const customEvent = new KeyboardEvent('keydown', { key: 'y', ctrlKey: true });
      Object.defineProperty(customEvent, 'target', { value: pdfViewer });
      document.dispatchEvent(customEvent);

      expect(customShortcut.action).not.toHaveBeenCalled();

      document.body.removeChild(pdfViewer);
    });
  });

  describe('Enable/Disable Functionality', () => {
    it('should disable all shortcuts when disabled', () => {
      keyboardHandler.disable();

      const pdfViewer = document.createElement('div');
      pdfViewer.setAttribute('data-pdf-viewer', 'true');
      document.body.appendChild(pdfViewer);

      const arrowEvent = new KeyboardEvent('keydown', { key: 'ArrowRight' });
      Object.defineProperty(arrowEvent, 'target', { value: pdfViewer });
      document.dispatchEvent(arrowEvent);

      expect(mockCallbacks.onNextPage).not.toHaveBeenCalled();

      document.body.removeChild(pdfViewer);
    });

    it('should re-enable shortcuts when enabled', () => {
      keyboardHandler.disable();
      keyboardHandler.enable();

      const pdfViewer = document.createElement('div');
      pdfViewer.setAttribute('data-pdf-viewer', 'true');
      document.body.appendChild(pdfViewer);

      const arrowEvent = new KeyboardEvent('keydown', { key: 'ArrowRight' });
      Object.defineProperty(arrowEvent, 'target', { value: pdfViewer });
      document.dispatchEvent(arrowEvent);

      expect(mockCallbacks.onNextPage).toHaveBeenCalled();

      document.body.removeChild(pdfViewer);
    });
  });

  describe('Accessibility Events', () => {
    it('should dispatch accessibility navigation events', () => {
      const eventListener = vi.fn();
      document.addEventListener('pdf-accessibility-navigate', eventListener);

      const pdfViewer = document.createElement('div');
      pdfViewer.setAttribute('data-pdf-viewer', 'true');
      document.body.appendChild(pdfViewer);

      // Test heading navigation
      const headingEvent = new KeyboardEvent('keydown', { key: 'h' });
      Object.defineProperty(headingEvent, 'target', { value: pdfViewer });
      document.dispatchEvent(headingEvent);

      expect(eventListener).toHaveBeenCalled();
      const event = eventListener.mock.calls[0][0];
      expect(event.detail.type).toBe('navigate-next-heading');

      document.body.removeChild(pdfViewer);
      document.removeEventListener('pdf-accessibility-navigate', eventListener);
    });
  });

  describe('Cleanup', () => {
    it('should cleanup event listeners on destroy', () => {
      const removeEventListenerSpy = vi.spyOn(document, 'removeEventListener');
      
      keyboardHandler.destroy();
      
      expect(removeEventListenerSpy).toHaveBeenCalledWith('keydown', expect.any(Function));
      expect(removeEventListenerSpy).toHaveBeenCalledWith('focusin', expect.any(Function));
      expect(removeEventListenerSpy).toHaveBeenCalledWith('focusout', expect.any(Function));
    });

    it('should clear shortcuts on destroy', () => {
      expect(keyboardHandler.getShortcuts().length).toBeGreaterThan(0);
      
      keyboardHandler.destroy();
      
      expect(keyboardHandler.getShortcuts().length).toBe(0);
    });
  });
});
