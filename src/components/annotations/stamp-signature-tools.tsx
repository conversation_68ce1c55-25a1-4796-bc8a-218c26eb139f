"use client";

import React, { useState, useCallback, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Stamp,
  PenTool,
  Type,
  Image,
  Calendar,
  CheckCircle,
  Info,
  Star,
  Shield,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { AdvancedAnnotation } from '@/lib/annotations/annotation-engine';

export interface StampTemplate {
  id: string;
  name: string;
  category: 'approval' | 'status' | 'date' | 'custom' | 'signature';
  template: string;
  variables: string[];
  size: 'small' | 'medium' | 'large';
  color: string;
  shape: 'rectangle' | 'circle' | 'diamond' | 'custom';
  preview: string;
  isCustom?: boolean;
}

export interface SignatureData {
  id: string;
  name: string;
  type: 'drawn' | 'typed' | 'image' | 'digital';
  data: string; // SVG path, image data, or certificate data
  createdAt: Date;
  isDefault?: boolean;
  metadata?: {
    signedBy?: string;
    certificate?: {
      issuer: string;
      subject: string;
      validFrom: Date;
      validTo: Date;
      fingerprint: string;
    };
  };
}

interface StampSignatureToolsProps {
  onStampAdd: (stamp: Partial<AdvancedAnnotation>) => void;
  onSignatureAdd: (signature: Partial<AdvancedAnnotation>) => void;
  savedStamps?: StampTemplate[];
  savedSignatures?: SignatureData[];
  onStampSave?: (stamp: StampTemplate) => void;
  onSignatureSave?: (signature: SignatureData) => void;
  onStampDelete?: (stampId: string) => void;
  onSignatureDelete?: (signatureId: string) => void;
  className?: string;
}

const DEFAULT_STAMPS: StampTemplate[] = [
  {
    id: 'approved',
    name: 'Approved',
    category: 'approval',
    template: 'APPROVED',
    variables: [],
    size: 'medium',
    color: '#22C55E',
    shape: 'rectangle',
    preview: 'APPROVED',
  },
  {
    id: 'rejected',
    name: 'Rejected',
    category: 'approval',
    template: 'REJECTED',
    variables: [],
    size: 'medium',
    color: '#EF4444',
    shape: 'rectangle',
    preview: 'REJECTED',
  },
  {
    id: 'reviewed',
    name: 'Reviewed',
    category: 'status',
    template: 'REVIEWED',
    variables: [],
    size: 'medium',
    color: '#3B82F6',
    shape: 'rectangle',
    preview: 'REVIEWED',
  },
  {
    id: 'confidential',
    name: 'Confidential',
    category: 'status',
    template: 'CONFIDENTIAL',
    variables: [],
    size: 'large',
    color: '#DC2626',
    shape: 'rectangle',
    preview: 'CONFIDENTIAL',
  },
  {
    id: 'draft',
    name: 'Draft',
    category: 'status',
    template: 'DRAFT',
    variables: [],
    size: 'medium',
    color: '#F59E0B',
    shape: 'rectangle',
    preview: 'DRAFT',
  },
  {
    id: 'date-stamp',
    name: 'Date Stamp',
    category: 'date',
    template: 'DATE: {date}',
    variables: ['date'],
    size: 'medium',
    color: '#6B7280',
    shape: 'rectangle',
    preview: 'DATE: 2024-01-15',
  },
  {
    id: 'signed-by',
    name: 'Signed By',
    category: 'signature',
    template: 'SIGNED BY: {name}\nDATE: {date}',
    variables: ['name', 'date'],
    size: 'large',
    color: '#059669',
    shape: 'rectangle',
    preview: 'SIGNED BY: John Doe\nDATE: 2024-01-15',
  },
];

export default function StampSignatureTools({
  onStampAdd,
  onSignatureAdd,
  savedStamps = [],
  savedSignatures = [],

  className,
}: StampSignatureToolsProps) {
  const [selectedStamp, setSelectedStamp] = useState<StampTemplate | null>(null);
  const [selectedSignature, setSelectedSignature] = useState<SignatureData | null>(null);
  const [customStampText, setCustomStampText] = useState('');
  const [customStampColor, setCustomStampColor] = useState('#3B82F6');
  const [customStampSize, setCustomStampSize] = useState<'small' | 'medium' | 'large'>('medium');
  const [signatureText, setSignatureText] = useState('');
  const [signatureFont, setSignatureFont] = useState('cursive');
  const [isDrawingSignature, setIsDrawingSignature] = useState(false);
  
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const isDrawing = useRef(false);
  const lastPoint = useRef<{ x: number; y: number } | null>(null);

  const allStamps = [...DEFAULT_STAMPS, ...savedStamps];

  // Handle stamp selection and application
  const handleStampSelect = useCallback((stamp: StampTemplate) => {
    setSelectedStamp(stamp);
    
    // Apply variables if needed
    let stampText = stamp.template;
    stamp.variables.forEach(variable => {
      switch (variable) {
        case 'date':
          stampText = stampText.replace('{date}', new Date().toLocaleDateString());
          break;
        case 'name':
          stampText = stampText.replace('{name}', 'Current User'); // Would come from auth
          break;
        case 'time':
          stampText = stampText.replace('{time}', new Date().toLocaleTimeString());
          break;
      }
    });

    onStampAdd({
      type: 'stamp',
      content: stampText,
      color: stamp.color,
      stamp: {
        type: 'predefined',
        template: stamp.template,
        size: stamp.size,
      },
      width: stamp.size === 'small' ? 80 : stamp.size === 'medium' ? 120 : 160,
      height: stamp.size === 'small' ? 40 : stamp.size === 'medium' ? 60 : 80,
    });
  }, [onStampAdd]);

  // Handle custom stamp creation
  const handleCustomStampAdd = useCallback(() => {
    if (!customStampText.trim()) return;

    const customStamp: Partial<AdvancedAnnotation> = {
      type: 'stamp',
      content: customStampText,
      color: customStampColor,
      stamp: {
        type: 'custom',
        template: customStampText,
        size: customStampSize,
      },
      width: customStampSize === 'small' ? 80 : customStampSize === 'medium' ? 120 : 160,
      height: customStampSize === 'small' ? 40 : customStampSize === 'medium' ? 60 : 80,
    };

    onStampAdd(customStamp);
    setCustomStampText('');
    setShowStampDialog(false);
  }, [customStampText, customStampColor, customStampSize, onStampAdd]);

  // Handle typed signature creation
  const handleTypedSignatureAdd = useCallback(() => {
    if (!signatureText.trim()) return;

    const signature: Partial<AdvancedAnnotation> = {
      type: 'signature',
      content: signatureText,
      signature: {
        type: 'typed',
        data: signatureText,
        signedBy: 'Current User',
        signedAt: new Date(),
      },
      fontFamily: signatureFont,
      fontSize: 24,
      width: 200,
      height: 60,
    };

    onSignatureAdd(signature);
    setSignatureText('');
    setShowSignatureDialog(false);
  }, [signatureText, signatureFont, onSignatureAdd]);

  // Canvas drawing for signature
  const startDrawing = useCallback((e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    isDrawing.current = true;
    lastPoint.current = { x, y };

    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.beginPath();
      ctx.moveTo(x, y);
    }
  }, []);

  const draw = useCallback((e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing.current || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const ctx = canvas.getContext('2d');
    if (ctx && lastPoint.current) {
      ctx.strokeStyle = '#000000';
      ctx.lineWidth = 2;
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';
      
      ctx.lineTo(x, y);
      ctx.stroke();
      
      lastPoint.current = { x, y };
    }
  }, []);

  const stopDrawing = useCallback(() => {
    isDrawing.current = false;
    lastPoint.current = null;
  }, []);

  // Save drawn signature
  const handleDrawnSignatureSave = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const dataURL = canvas.toDataURL();
    
    const signature: Partial<AdvancedAnnotation> = {
      type: 'signature',
      signature: {
        type: 'drawn',
        data: dataURL,
        signedBy: 'Current User',
        signedAt: new Date(),
      },
      imageUrl: dataURL,
      width: 200,
      height: 100,
    };

    onSignatureAdd(signature);
    
    // Clear canvas
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
    }
    
    setShowSignatureDialog(false);
  }, [onSignatureAdd]);

  // Clear signature canvas
  const clearSignatureCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
    }
  }, []);

  const renderStampPreview = (stamp: StampTemplate) => {
    const Icon = stamp.category === 'approval' ? CheckCircle :
                stamp.category === 'status' ? Info :
                stamp.category === 'date' ? Calendar :
                stamp.category === 'signature' ? PenTool : Stamp;

    return (
      <div
        className={cn(
          "p-3 border rounded-lg cursor-pointer transition-all hover:shadow-md",
          selectedStamp?.id === stamp.id && "border-primary bg-primary/5"
        )}
        onClick={() => handleStampSelect(stamp)}
      >
        <div className="flex items-center gap-2 mb-2">
          <Icon className="h-4 w-4" style={{ color: stamp.color }} />
          <span className="text-sm font-medium">{stamp.name}</span>
          {stamp.isCustom && <Badge variant="outline" className="text-xs">Custom</Badge>}
        </div>
        <div
          className="text-xs p-2 rounded text-center font-mono whitespace-pre-line"
          style={{
            backgroundColor: stamp.color + '20',
            color: stamp.color,
            border: `1px solid ${stamp.color}40`,
          }}
        >
          {stamp.preview}
        </div>
      </div>
    );
  };

  const renderSignaturePreview = (signature: SignatureData) => {
    const Icon = signature.type === 'drawn' ? PenTool :
                signature.type === 'typed' ? Type :
                signature.type === 'image' ? Image : Shield;

    return (
      <div
        className={cn(
          "p-3 border rounded-lg cursor-pointer transition-all hover:shadow-md",
          selectedSignature?.id === signature.id && "border-primary bg-primary/5"
        )}
        onClick={() => setSelectedSignature(signature)}
      >
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <Icon className="h-4 w-4" />
            <span className="text-sm font-medium">{signature.name}</span>
          </div>
          {signature.isDefault && <Star className="h-3 w-3 text-yellow-500" />}
        </div>
        <div className="text-xs text-muted-foreground mb-2">
          {signature.type} • {signature.createdAt.toLocaleDateString()}
        </div>
        <div className="h-12 bg-muted rounded flex items-center justify-center">
          {signature.type === 'drawn' || signature.type === 'image' ? (
            <img
              src={signature.data}
              alt="Signature preview"
              className="max-h-full max-w-full object-contain"
            />
          ) : (
            <span className="text-sm italic" style={{ fontFamily: 'cursive' }}>
              {signature.data}
            </span>
          )}
        </div>
      </div>
    );
  };

  return (
    <Card className={cn("w-80", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-base">Stamps & Signatures</CardTitle>
        <CardDescription>Add stamps and digital signatures to documents</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="stamps" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="stamps">Stamps</TabsTrigger>
            <TabsTrigger value="signatures">Signatures</TabsTrigger>
          </TabsList>

          <TabsContent value="stamps" className="space-y-4">
            {/* Quick Stamps */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">Quick Stamps</Label>
                <Dialog open={showStampDialog} onOpenChange={setShowStampDialog}>
                  <DialogTrigger asChild>
                    <Button size="sm" variant="outline">
                      <Edit className="h-3 w-3 mr-1" />
                      Custom
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Create Custom Stamp</DialogTitle>
                      <DialogDescription>
                        Design your own custom stamp with text and styling
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label>Stamp Text</Label>
                        <Textarea
                          value={customStampText}
                          onChange={(e) => setCustomStampText(e.target.value)}
                          placeholder="Enter stamp text..."
                          rows={3}
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>Color</Label>
                          <Input
                            type="color"
                            value={customStampColor}
                            onChange={(e) => setCustomStampColor(e.target.value)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Size</Label>
                          <Select value={customStampSize} onValueChange={(value: 'small' | 'medium' | 'large') => setCustomStampSize(value)}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="small">Small</SelectItem>
                              <SelectItem value="medium">Medium</SelectItem>
                              <SelectItem value="large">Large</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button onClick={handleCustomStampAdd} className="flex-1">
                          Add Stamp
                        </Button>
                        <Button variant="outline" onClick={() => setShowStampDialog(false)}>
                          Cancel
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>

              <ScrollArea className="h-64">
                <div className="grid gap-2">
                  {allStamps.map(renderStampPreview)}
                </div>
              </ScrollArea>
            </div>
          </TabsContent>

          <TabsContent value="signatures" className="space-y-4">
            {/* Signature Options */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">Signatures</Label>
                <Dialog open={showSignatureDialog} onOpenChange={setShowSignatureDialog}>
                  <DialogTrigger asChild>
                    <Button size="sm" variant="outline">
                      <PenTool className="h-3 w-3 mr-1" />
                      Create
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-md">
                    <DialogHeader>
                      <DialogTitle>Create Signature</DialogTitle>
                      <DialogDescription>
                        Create a new signature by typing or drawing
                      </DialogDescription>
                    </DialogHeader>
                    <Tabs defaultValue="type" className="w-full">
                      <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="type">Type</TabsTrigger>
                        <TabsTrigger value="draw">Draw</TabsTrigger>
                      </TabsList>
                      
                      <TabsContent value="type" className="space-y-4">
                        <div className="space-y-2">
                          <Label>Your Name</Label>
                          <Input
                            value={signatureText}
                            onChange={(e) => setSignatureText(e.target.value)}
                            placeholder="Enter your name..."
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Font Style</Label>
                          <Select value={signatureFont} onValueChange={setSignatureFont}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="cursive">Cursive</SelectItem>
                              <SelectItem value="serif">Serif</SelectItem>
                              <SelectItem value="sans-serif">Sans Serif</SelectItem>
                              <SelectItem value="monospace">Monospace</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="p-4 border rounded bg-muted">
                          <div
                            className="text-2xl text-center"
                            style={{ fontFamily: signatureFont }}
                          >
                            {signatureText || 'Your signature preview'}
                          </div>
                        </div>
                        <Button onClick={handleTypedSignatureAdd} className="w-full">
                          Add Signature
                        </Button>
                      </TabsContent>
                      
                      <TabsContent value="draw" className="space-y-4">
                        <div className="space-y-2">
                          <Label>Draw Your Signature</Label>
                          <div className="border rounded">
                            <canvas
                              ref={canvasRef}
                              width={300}
                              height={150}
                              className="w-full cursor-crosshair"
                              onMouseDown={startDrawing}
                              onMouseMove={draw}
                              onMouseUp={stopDrawing}
                              onMouseLeave={stopDrawing}
                            />
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button onClick={handleDrawnSignatureSave} className="flex-1">
                            Save Signature
                          </Button>
                          <Button variant="outline" onClick={clearSignatureCanvas}>
                            Clear
                          </Button>
                        </div>
                      </TabsContent>
                    </Tabs>
                  </DialogContent>
                </Dialog>
              </div>

              <ScrollArea className="h-64">
                <div className="grid gap-2">
                  {savedSignatures.map(renderSignaturePreview)}
                  {savedSignatures.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      <PenTool className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No signatures saved</p>
                      <p className="text-xs">Create your first signature above</p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
