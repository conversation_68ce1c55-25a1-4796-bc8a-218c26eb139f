"use client";

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Download,
  Upload,
  PenTool,
  MousePointer,
  Square,
  Highlighter,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

import AdvancedDrawingTools from './advanced-drawing-tools';
import StampSignatureTools from './stamp-signature-tools';
import AdvancedAnnotationCanvas from './advanced-annotation-canvas';
import { AnnotationEngine, type AdvancedAnnotation, type AnnotationStyle } from '@/lib/annotations/annotation-engine';

interface AdvancedAnnotationManagerProps {
  pageNumber: number;
  scale: number;
  isReadOnly?: boolean;
  onAnnotationsChange?: (annotations: AdvancedAnnotation[]) => void;
  initialAnnotations?: AdvancedAnnotation[];
  className?: string;
}

const DEFAULT_STYLE: AnnotationStyle = {
  stroke: {
    color: '#000000',
    width: 2,
    opacity: 1,
    style: 'solid',
    lineCap: 'round',
    lineJoin: 'round',
  },
  fill: {
    color: '#3B82F6',
    opacity: 0.3,
  },
  text: {
    fontFamily: 'Arial, sans-serif',
    fontSize: 14,
    fontWeight: 'normal',
    fontStyle: 'normal',
    textDecoration: 'none',
    textAlign: 'left',
    lineHeight: 1.4,
    letterSpacing: 0,
    wordSpacing: 0,
  },
  effects: {
    opacity: 1,
  },
};

export default function AdvancedAnnotationManager({
  pageNumber,
  scale,
  isReadOnly = false,
  onAnnotationsChange,
  initialAnnotations = [],
  className,
}: AdvancedAnnotationManagerProps) {
  const [annotationEngine] = useState(() => new AnnotationEngine());
  const [selectedTool, setSelectedTool] = useState('select');
  const [currentStyle, setCurrentStyle] = useState<AnnotationStyle>(DEFAULT_STYLE);
  const [selectedAnnotation, setSelectedAnnotation] = useState<AdvancedAnnotation | null>(null);
  const [showGrid, setShowGrid] = useState(false);
  const [showRulers, setShowRulers] = useState(false);
  const [layers, setLayers] = useState([
    { id: 'layer-1', name: 'Layer 1', visible: true, locked: false },
  ]);
  const [activeLayer, setActiveLayer] = useState('layer-1');

  // Initialize annotations
  useEffect(() => {
    initialAnnotations.forEach(annotation => {
      annotationEngine.annotations.set(annotation.id, annotation);
    });
  }, [annotationEngine, initialAnnotations]);

  // Get annotations for current page
  const pageAnnotations = useMemo(() => {
    return annotationEngine.getAnnotationsForPage(pageNumber);
  }, [annotationEngine, pageNumber]);

  // Handle annotation creation
  const handleAnnotationAdd = useCallback((annotationData: Partial<AdvancedAnnotation>) => {
    annotationEngine.createAnnotation(
      annotationData.type || 'rectangle',
      pageNumber,
      { x: annotationData.x || 0, y: annotationData.y || 0 },
      currentStyle,
      {
        ...annotationData,
        layer: layers.find(l => l.id === activeLayer)?.id ? parseInt(activeLayer.split('-')[1]) : 0,
      }
    );

    onAnnotationsChange?.(annotationEngine.getAllAnnotations());
    toast.success('Annotation added');
  }, [annotationEngine, pageNumber, currentStyle, activeLayer, layers, onAnnotationsChange]);

  // Handle annotation update
  const handleAnnotationUpdate = useCallback((id: string, updates: Partial<AdvancedAnnotation>) => {
    const updated = annotationEngine.updateAnnotation(id, updates);
    if (updated) {
      onAnnotationsChange?.(annotationEngine.getAllAnnotations());
    }
  }, [annotationEngine, onAnnotationsChange]);



  // Handle annotation selection
  const handleAnnotationSelect = useCallback((annotation: AdvancedAnnotation | null) => {
    setSelectedAnnotation(annotation);
    if (annotation) {
      setSelectedTool('select');
    }
  }, []);

  // Handle style changes
  const handleStyleChange = useCallback((styleUpdates: Partial<AnnotationStyle>) => {
    setCurrentStyle(prev => ({
      ...prev,
      ...styleUpdates,
      stroke: { ...prev.stroke, ...styleUpdates.stroke },
      fill: { ...prev.fill, ...styleUpdates.fill },
      text: { ...prev.text, ...styleUpdates.text },
      effects: { ...prev.effects, ...styleUpdates.effects },
    }));

    // Apply to selected annotation if any
    if (selectedAnnotation) {
      const updates: Partial<AdvancedAnnotation> = {};
      
      if (styleUpdates.stroke?.color) {
        updates.color = styleUpdates.stroke.color;
        updates.strokeColor = styleUpdates.stroke.color;
      }
      if (styleUpdates.stroke?.width) {
        updates.strokeWidth = styleUpdates.stroke.width;
      }
      if (styleUpdates.fill?.color) {
        updates.fillColor = styleUpdates.fill.color;
      }
      if (styleUpdates.effects?.opacity) {
        updates.opacity = styleUpdates.effects.opacity;
      }

      if (Object.keys(updates).length > 0) {
        handleAnnotationUpdate(selectedAnnotation.id, updates);
      }
    }
  }, [selectedAnnotation, handleAnnotationUpdate]);

  // Handle undo/redo
  const handleUndo = useCallback(() => {
    const undone = annotationEngine.undo();
    if (undone) {
      onAnnotationsChange?.(annotationEngine.getAllAnnotations());
      toast.success('Undone');
    }
  }, [annotationEngine, onAnnotationsChange]);

  const handleRedo = useCallback(() => {
    const redone = annotationEngine.redo();
    if (redone) {
      onAnnotationsChange?.(annotationEngine.getAllAnnotations());
      toast.success('Redone');
    }
  }, [annotationEngine, onAnnotationsChange]);

  // Handle layer operations
  const handleLayerChange = useCallback((layerId: string) => {
    setActiveLayer(layerId);
  }, []);

  const handleLayerToggle = useCallback((layerId: string, property: 'visible' | 'locked') => {
    setLayers(prev => prev.map(layer => 
      layer.id === layerId 
        ? { ...layer, [property]: !layer[property] }
        : layer
    ));
  }, []);

  // Handle stamp addition
  const handleStampAdd = useCallback((stampData: Partial<AdvancedAnnotation>) => {
    handleAnnotationAdd({
      ...stampData,
      type: 'stamp',
    });
  }, [handleAnnotationAdd]);

  // Handle signature addition
  const handleSignatureAdd = useCallback((signatureData: Partial<AdvancedAnnotation>) => {
    handleAnnotationAdd({
      ...signatureData,
      type: 'signature',
    });
  }, [handleAnnotationAdd]);

  // Export annotations
  const handleExport = useCallback((format: 'json' | 'pdf' | 'xfdf' | 'csv') => {
    try {
      const exported = annotationEngine.exportAnnotations(format);
      
      // Create download
      const blob = new Blob([exported], { 
        type: format === 'json' ? 'application/json' : 'text/plain' 
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `annotations.${format}`;
      link.click();
      URL.revokeObjectURL(url);
      
      toast.success(`Annotations exported as ${format.toUpperCase()}`);
    } catch {
      toast.error('Failed to export annotations');
    }
  }, [annotationEngine]);

  // Import annotations
  const handleImport = useCallback((file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = e.target?.result as string;
        const format = file.name.endsWith('.json') ? 'json' : 'xfdf';
        const imported = annotationEngine.importAnnotations(data, format);
        
        if (imported > 0) {
          onAnnotationsChange?.(annotationEngine.getAllAnnotations());
          toast.success(`Imported ${imported} annotations`);
        } else {
          toast.error('No annotations found in file');
        }
      } catch {
        toast.error('Failed to import annotations');
      }
    };
    reader.readAsText(file);
  }, [annotationEngine, onAnnotationsChange]);

  // Clear all annotations
  const handleClearAll = useCallback(() => {
    annotationEngine.clearAll();
    setSelectedAnnotation(null);
    onAnnotationsChange?.(annotationEngine.getAllAnnotations());
    toast.success('All annotations cleared');
  }, [annotationEngine, onAnnotationsChange]);

  const canUndo = annotationEngine.currentHistoryIndex > 0;
  const canRedo = annotationEngine.currentHistoryIndex < annotationEngine.history.length - 1;

  return (
    <div className={cn("flex h-full", className)}>
      {/* Main Canvas Area */}
      <div className="flex-1 relative">
        <AdvancedAnnotationCanvas
          pageNumber={pageNumber}
          annotations={pageAnnotations}
          selectedTool={selectedTool}
          currentStyle={currentStyle}
          scale={scale}
          onAnnotationAdd={handleAnnotationAdd}
          onAnnotationUpdate={handleAnnotationUpdate}
          onAnnotationSelect={handleAnnotationSelect}
          selectedAnnotation={selectedAnnotation}
          showGrid={showGrid}
          showRulers={showRulers}
          isReadOnly={isReadOnly}
        />

        {/* Floating toolbar */}
        {!isReadOnly && (
          <div className="absolute top-4 left-4 flex gap-2">
            <Button
              variant={selectedTool === 'select' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedTool('select')}
            >
              <MousePointer className="h-4 w-4" />
            </Button>
            <Button
              variant={selectedTool === 'pen' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedTool('pen')}
            >
              <PenTool className="h-4 w-4" />
            </Button>
            <Button
              variant={selectedTool === 'rectangle' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedTool('rectangle')}
            >
              <Square className="h-4 w-4" />
            </Button>
            <Button
              variant={selectedTool === 'text' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedTool('text')}
            >
              <Type className="h-4 w-4" />
            </Button>
            <Button
              variant={selectedTool === 'highlight' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedTool('highlight')}
            >
              <Highlighter className="h-4 w-4" />
            </Button>
          </div>
        )}

        {/* Status bar */}
        <div className="absolute bottom-4 left-4 flex items-center gap-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2 shadow-lg">
          <Badge variant="outline">
            Page {pageNumber}
          </Badge>
          <Badge variant="outline">
            {pageAnnotations.length} annotations
          </Badge>
          <Badge variant="outline">
            {Math.round(scale * 100)}% zoom
          </Badge>
          {selectedAnnotation && (
            <Badge variant="default">
              {selectedAnnotation.type} selected
            </Badge>
          )}
        </div>
      </div>

      {/* Right Sidebar */}
      {!isReadOnly && (
        <div className="w-80 border-l bg-background">
          <Tabs defaultValue="tools" className="h-full">
            <div className="border-b p-3">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="tools">Tools</TabsTrigger>
                <TabsTrigger value="stamps">Stamps</TabsTrigger>
                <TabsTrigger value="layers">Layers</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="tools" className="h-full p-0">
              <AdvancedDrawingTools
                selectedTool={selectedTool}
                onToolSelect={setSelectedTool}
                currentStyle={currentStyle}
                onStyleChange={handleStyleChange}
                onUndo={handleUndo}
                onRedo={handleRedo}
                canUndo={canUndo}
                canRedo={canRedo}
                onClear={handleClearAll}
                showGrid={showGrid}
                onGridToggle={setShowGrid}
                showRulers={showRulers}
                onRulersToggle={setShowRulers}
                layers={layers}
                activeLayer={activeLayer}
                onLayerChange={handleLayerChange}
                onLayerToggle={handleLayerToggle}
                className="border-0"
              />
            </TabsContent>

            <TabsContent value="stamps" className="h-full p-0">
              <StampSignatureTools
                onStampAdd={handleStampAdd}
                onSignatureAdd={handleSignatureAdd}
                className="border-0"
              />
            </TabsContent>

            <TabsContent value="layers" className="h-full p-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base">Annotation Management</CardTitle>
                  <CardDescription>Manage layers and export options</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Export/Import */}
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Export/Import</h4>
                    <div className="grid grid-cols-2 gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleExport('json')}
                      >
                        <Download className="h-3 w-3 mr-1" />
                        JSON
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleExport('csv')}
                      >
                        <Download className="h-3 w-3 mr-1" />
                        CSV
                      </Button>
                    </div>
                    <input
                      type="file"
                      accept=".json,.xfdf"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) handleImport(file);
                      }}
                      className="hidden"
                      id="import-annotations"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => document.getElementById('import-annotations')?.click()}
                      className="w-full"
                    >
                      <Upload className="h-3 w-3 mr-1" />
                      Import
                    </Button>
                  </div>

                  <Separator />

                  {/* Statistics */}
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Statistics</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <div className="font-medium">{pageAnnotations.length}</div>
                        <div className="text-muted-foreground">This Page</div>
                      </div>
                      <div>
                        <div className="font-medium">{annotationEngine.getAllAnnotations().length}</div>
                        <div className="text-muted-foreground">Total</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      )}
    </div>
  );
}
