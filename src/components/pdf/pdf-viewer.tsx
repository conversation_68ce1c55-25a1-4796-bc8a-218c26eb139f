"use client";

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  FileText, 
  Loader2, 
  AlertTriangle,
  Settings,
  Search,
  Accessibility,
  Keyboard,
  Smartphone,
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Import all our components and libraries
import { PDFErrorBoundary } from '@/components/pdf/error-boundary';
import { ProgressivePDFLoader } from '@/components/pdf/progressive-pdf-loader';
import { WorkerManagerDashboard } from '@/components/pdf/worker-manager-dashboard';
import QualityControls from '@/components/pdf/quality-controls';
import MemoryMonitor from '@/components/pdf/memory-monitor';
import AccessibilityPanel from '@/components/accessibility/accessibility-panel';
import KeyboardShortcutsHelp from '@/components/navigation/keyboard-shortcuts-help';
import SearchPanel from '@/components/search/search-panel';
import MobileControls, { useMobileControls } from '@/components/mobile/mobile-controls';

// Import our core libraries
import { ProgressivePDFLoader as PDFLoaderCore } from '@/lib/pdf/progressive-loader';
import { PDFWorkerManager } from '@/lib/pdf/worker-manager';
import { QualityManager } from '@/lib/pdf/quality-manager';
import { MemoryManager } from '@/lib/pdf/memory-manager';
import { PDFErrorHandler } from '@/lib/pdf/error-handler';
import { AccessibilityManager } from '@/lib/accessibility/screen-reader';
import { KeyboardNavigationHandler } from '@/lib/navigation/keyboard-handler';
import { PDFSearchEngine } from '@/lib/search/pdf-search';
import { TouchGestureHandler } from '@/lib/mobile/touch-gestures';

interface PDFViewerProps {
  url?: string;
  file?: File;
  className?: string;
  onDocumentLoad?: (document: any) => void;
  onPageChange?: (page: number) => void;
  onError?: (error: Error) => void;
}

export default function PDFViewer({
  url,
  file,
  className,
  onDocumentLoad,
  onPageChange,
  onError,
}: PDFViewerProps) {
  // State management
  const [document, setDocument] = useState<any>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [showControls, setShowControls] = useState(false);
  const [activePanel, setActivePanel] = useState<string | null>(null);

  // Refs for managers
  const viewerRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const pdfLoaderRef = useRef<PDFLoaderCore | null>(null);
  const workerManagerRef = useRef<PDFWorkerManager | null>(null);
  const qualityManagerRef = useRef<QualityManager | null>(null);
  const memoryManagerRef = useRef<MemoryManager | null>(null);
  const errorHandlerRef = useRef<PDFErrorHandler | null>(null);
  const accessibilityManagerRef = useRef<AccessibilityManager | null>(null);
  const keyboardHandlerRef = useRef<KeyboardNavigationHandler | null>(null);
  const searchEngineRef = useRef<PDFSearchEngine | null>(null);
  const gestureHandlerRef = useRef<TouchGestureHandler | null>(null);

  // Mobile detection
  const { isMobile, orientation } = useMobileControls();

  // Initialize managers
  useEffect(() => {
    // Initialize core managers
    workerManagerRef.current = new PDFWorkerManager();
    qualityManagerRef.current = new QualityManager();
    memoryManagerRef.current = new MemoryManager();
    errorHandlerRef.current = new PDFErrorHandler();
    accessibilityManagerRef.current = new AccessibilityManager();
    searchEngineRef.current = new PDFSearchEngine();

    // Initialize keyboard navigation
    keyboardHandlerRef.current = new KeyboardNavigationHandler({
      onNextPage: () => handlePageChange(Math.min(currentPage + 1, totalPages)),
      onPreviousPage: () => handlePageChange(Math.max(currentPage - 1, 1)),
      onFirstPage: () => handlePageChange(1),
      onLastPage: () => handlePageChange(totalPages),
      onZoomIn: () => handleZoomChange(Math.min(zoomLevel + 0.25, 3)),
      onZoomOut: () => handleZoomChange(Math.max(zoomLevel - 0.25, 0.5)),
      onZoomReset: () => handleZoomChange(1),
      onZoomFit: () => handleZoomFit(),
      onZoomFitWidth: () => handleZoomFitWidth(),
      onRotateClockwise: () => handleRotate(),
      onToggleFullscreen: () => handleToggleFullscreen(),
      onToggleSidebar: () => setShowControls(!showControls),
      onSearch: () => setActivePanel(activePanel === 'search' ? null : 'search'),
      onGoToPage: (page) => handlePageChange(page),
      onEscape: () => setActivePanel(null),
    });

    // Initialize touch gestures for mobile
    if (isMobile && viewerRef.current) {
      gestureHandlerRef.current = new TouchGestureHandler(viewerRef.current, {
        onDoubleTap: () => handleZoomFit(),
        onPinchMove: (event) => handleZoomChange(zoomLevel * event.scale),
        onSwipe: (event) => {
          if (event.direction === 'left') {
            handlePageChange(Math.min(currentPage + 1, totalPages));
          } else if (event.direction === 'right') {
            handlePageChange(Math.max(currentPage - 1, 1));
          }
        },
      });
    }

    return () => {
      // Cleanup all managers
      workerManagerRef.current?.destroy();
      qualityManagerRef.current?.destroy();
      memoryManagerRef.current?.destroy();
      errorHandlerRef.current?.destroy();
      accessibilityManagerRef.current?.destroy();
      keyboardHandlerRef.current?.destroy();
      searchEngineRef.current?.destroy();
      gestureHandlerRef.current?.destroy();
    };
  }, [isMobile]);

  // Load PDF document
  useEffect(() => {
    if (!url && !file) return;

    const loadDocument = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Initialize progressive loader
        pdfLoaderRef.current = new PDFLoaderCore({
          enableRangeRequests: true,
          chunkSize: 1024 * 1024, // 1MB chunks
          enableCompression: true,
        });

        // Load document
        const doc = file 
          ? await pdfLoaderRef.current.loadFromFile(file)
          : await pdfLoaderRef.current.loadFromUrl(url!);

        setDocument(doc);
        setTotalPages(doc.numPages);
        setCurrentPage(1);

        // Initialize search engine with document
        searchEngineRef.current?.setDocument(doc);

        // Announce document load
        accessibilityManagerRef.current?.announceLoadingState('loaded');

        onDocumentLoad?.(doc);
      } catch (err) {
        const error = err instanceof Error ? err : new Error('Failed to load PDF');
        setError(error);
        errorHandlerRef.current?.handleError(error);
        accessibilityManagerRef.current?.announceLoadingState('error', error.message);
        onError?.(error);
      } finally {
        setIsLoading(false);
      }
    };

    loadDocument();
  }, [url, file, onDocumentLoad, onError]);

  // Handle page changes
  const handlePageChange = useCallback((page: number) => {
    if (page < 1 || page > totalPages) return;
    
    setCurrentPage(page);
    accessibilityManagerRef.current?.announcePageChange(page, totalPages);
    onPageChange?.(page);
  }, [totalPages, onPageChange]);

  // Handle zoom changes
  const handleZoomChange = useCallback((zoom: number) => {
    const newZoom = Math.max(0.5, Math.min(3, zoom));
    setZoomLevel(newZoom);
    accessibilityManagerRef.current?.announceZoomChange(newZoom);
  }, []);

  // Handle rotation
  const handleRotate = useCallback(() => {
    setRotation(prev => (prev + 90) % 360);
  }, []);

  // Handle zoom fit
  const handleZoomFit = useCallback(() => {
    // Calculate zoom to fit page
    if (viewerRef.current && canvasRef.current) {
      const containerWidth = viewerRef.current.clientWidth;
      const containerHeight = viewerRef.current.clientHeight;
      const pageWidth = canvasRef.current.width;
      const pageHeight = canvasRef.current.height;
      
      const scaleX = containerWidth / pageWidth;
      const scaleY = containerHeight / pageHeight;
      const scale = Math.min(scaleX, scaleY) * 0.9; // 90% to add padding
      
      handleZoomChange(scale);
    }
  }, [handleZoomChange]);

  // Handle zoom fit width
  const handleZoomFitWidth = useCallback(() => {
    if (viewerRef.current && canvasRef.current) {
      const containerWidth = viewerRef.current.clientWidth;
      const pageWidth = canvasRef.current.width;
      const scale = (containerWidth / pageWidth) * 0.95; // 95% to add padding
      
      handleZoomChange(scale);
    }
  }, [handleZoomChange]);

  // Handle fullscreen toggle
  const handleToggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      viewerRef.current?.requestFullscreen();
    } else {
      document.exitFullscreen();
    }
  }, []);

  // Render loading state
  if (isLoading) {
    return (
      <Card className={cn("flex items-center justify-center h-96", className)}>
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin mx-auto" />
          <p className="text-muted-foreground">Loading PDF document...</p>
          {pdfLoaderRef.current && (
            <ProgressivePDFLoader 
              loader={pdfLoaderRef.current}
              className="w-full max-w-md"
            />
          )}
        </div>
      </Card>
    );
  }

  // Render error state
  if (error) {
    return (
      <Card className={cn("p-6", className)}>
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Failed to load PDF: {error.message}
          </AlertDescription>
        </Alert>
      </Card>
    );
  }

  // Render main viewer
  return (
    <PDFErrorBoundary>
      <div 
        ref={viewerRef}
        className={cn("relative w-full h-full bg-gray-100 dark:bg-gray-900", className)}
        data-pdf-viewer="true"
      >
        {/* Main Canvas */}
        <div className="absolute inset-0 overflow-auto">
          <div 
            className="flex items-center justify-center min-h-full p-4"
            style={{
              transform: `rotate(${rotation}deg) scale(${zoomLevel})`,
              transformOrigin: 'center',
            }}
          >
            <canvas
              ref={canvasRef}
              className="shadow-lg bg-white"
              style={{
                maxWidth: '100%',
                maxHeight: '100%',
              }}
            />
          </div>
        </div>

        {/* Mobile Controls */}
        {isMobile && (
          <MobileControls
            currentPage={currentPage}
            totalPages={totalPages}
            zoomLevel={zoomLevel}
            rotation={rotation}
            onPageChange={handlePageChange}
            onZoomChange={handleZoomChange}
            onRotate={handleRotate}
            onSearch={() => setActivePanel('search')}
            onToggleMenu={() => setShowControls(!showControls)}
            onToggleFullscreen={handleToggleFullscreen}
          />
        )}

        {/* Desktop Controls */}
        {!isMobile && (
          <div className="absolute top-4 right-4 flex flex-col gap-2">
            <Button
              variant="secondary"
              size="sm"
              onClick={() => setShowControls(!showControls)}
            >
              <Settings className="h-4 w-4" />
            </Button>
            
            <Button
              variant="secondary"
              size="sm"
              onClick={() => setActivePanel(activePanel === 'search' ? null : 'search')}
            >
              <Search className="h-4 w-4" />
            </Button>
            
            <Button
              variant="secondary"
              size="sm"
              onClick={() => setActivePanel(activePanel === 'accessibility' ? null : 'accessibility')}
            >
              <Accessibility className="h-4 w-4" />
            </Button>
            
            {keyboardHandlerRef.current && (
              <KeyboardShortcutsHelp
                keyboardHandler={keyboardHandlerRef.current}
                trigger={
                  <Button variant="secondary" size="sm">
                    <Keyboard className="h-4 w-4" />
                  </Button>
                }
              />
            )}
          </div>
        )}

        {/* Side Panels */}
        {showControls && (
          <div className="absolute top-0 left-0 w-80 h-full bg-background border-r shadow-lg overflow-y-auto">
            <div className="p-4 space-y-6">
              {/* Quality Controls */}
              {qualityManagerRef.current && (
                <QualityControls 
                  qualityManager={qualityManagerRef.current}
                />
              )}
              
              {/* Worker Manager Dashboard */}
              {workerManagerRef.current && (
                <WorkerManagerDashboard 
                  workerManager={workerManagerRef.current}
                />
              )}
              
              {/* Memory Monitor */}
              {memoryManagerRef.current && (
                <MemoryMonitor 
                  memoryManager={memoryManagerRef.current}
                />
              )}
            </div>
          </div>
        )}

        {/* Search Panel */}
        {activePanel === 'search' && searchEngineRef.current && (
          <div className="absolute top-0 right-0 w-96 h-full bg-background border-l shadow-lg overflow-y-auto">
            <div className="p-4">
              <SearchPanel 
                searchEngine={searchEngineRef.current}
                onResultSelect={(result) => {
                  handlePageChange(result.pageNumber);
                  // TODO: Highlight search result
                }}
              />
            </div>
          </div>
        )}

        {/* Accessibility Panel */}
        {activePanel === 'accessibility' && accessibilityManagerRef.current && (
          <div className="absolute top-0 right-0 w-96 h-full bg-background border-l shadow-lg overflow-y-auto">
            <div className="p-4">
              <AccessibilityPanel 
                accessibilityManager={accessibilityManagerRef.current}
              />
            </div>
          </div>
        )}

        {/* Document Info */}
        <div className="absolute bottom-4 left-4">
          <Card className="bg-black/50 text-white border-white/20 backdrop-blur-sm">
            <div className="p-2 flex items-center gap-2 text-sm">
              <FileText className="h-4 w-4" />
              <span>Page {currentPage} of {totalPages}</span>
              <span>•</span>
              <span>{Math.round(zoomLevel * 100)}%</span>
              {rotation > 0 && (
                <>
                  <span>•</span>
                  <span>{rotation}°</span>
                </>
              )}
            </div>
          </Card>
        </div>
      </div>
    </PDFErrorBoundary>
  );
}
