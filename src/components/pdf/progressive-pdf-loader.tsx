"use client";

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Download, 
  Pause, 
  Play, 
  X, 
  FileText, 
  Zap, 
  Clock, 
  HardDrive,
  Wifi,
  CheckCircle,
  AlertCircle,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { ProgressivePDFLoader, type LoadingProgress, type LoadingEvent } from '@/lib/pdf/progressive-loader';
import * as pdfjsLib from 'pdfjs-dist';

interface ProgressivePDFLoaderProps {
  source: string | File;
  onDocumentLoaded?: (document: pdfjsLib.PDFDocumentProxy) => void;
  onError?: (error: Error) => void;
  onCancel?: () => void;
  enableStreaming?: boolean;
  chunkSize?: number;
  preloadPages?: number;
  className?: string;
}

interface LoadingStats {
  startTime: number;
  chunksLoaded: number;
  pagesReady: number;
  averageChunkTime: number;
  networkSpeed: string;
  estimatedCompletion: string;
}

export default function ProgressivePDFLoaderComponent({
  source,
  onDocumentLoaded,
  onError,
  onCancel,
  enableStreaming = true,
  chunkSize = 1024 * 1024, // 1MB
  preloadPages = 5,
  className,
}: ProgressivePDFLoaderProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [progress, setProgress] = useState<LoadingProgress | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [stats, setStats] = useState<LoadingStats>({
    startTime: 0,
    chunksLoaded: 0,
    pagesReady: 0,
    averageChunkTime: 0,
    networkSpeed: '0 KB/s',
    estimatedCompletion: 'Calculating...',
  });
  const [loadingPhase, setLoadingPhase] = useState<'initializing' | 'downloading' | 'processing' | 'complete'>('initializing');

  const loaderRef = useRef<ProgressivePDFLoader | null>(null);
  const chunkTimesRef = useRef<number[]>([]);
  const lastChunkTimeRef = useRef<number>(0);

  // Initialize loader
  useEffect(() => {
    loaderRef.current = new ProgressivePDFLoader({
      chunkSize,
      enableStreaming,
      preloadPages,
      maxConcurrentChunks: 3,
      enableRangeRequests: true,
      adaptiveQuality: true,
      networkThrottling: false,
    });

    const loader = loaderRef.current;

    // Set up event listeners
    loader.addEventListener('progress', handleProgress);
    loader.addEventListener('chunk-loaded', handleChunkLoaded);
    loader.addEventListener('page-ready', handlePageReady);
    loader.addEventListener('metadata-loaded', handleMetadataLoaded);
    loader.addEventListener('error', handleError);
    loader.addEventListener('complete', handleComplete);

    return () => {
      loader.cancel();
    };
  }, [chunkSize, enableStreaming, preloadPages]);

  const handleProgress = useCallback((event: LoadingEvent) => {
    setProgress(event.progress);
    setLoadingPhase('downloading');
    
    // Update network speed
    const speed = formatBytes(event.progress.downloadSpeed);
    setStats(prev => ({
      ...prev,
      networkSpeed: `${speed}/s`,
      estimatedCompletion: formatTime(event.progress.estimatedTimeRemaining),
    }));
  }, []);

  const handleChunkLoaded = useCallback((event: LoadingEvent) => {
    const now = Date.now();
    if (lastChunkTimeRef.current > 0) {
      const chunkTime = now - lastChunkTimeRef.current;
      chunkTimesRef.current.push(chunkTime);
      
      // Keep only last 10 chunk times for average
      if (chunkTimesRef.current.length > 10) {
        chunkTimesRef.current.shift();
      }
    }
    lastChunkTimeRef.current = now;

    setStats(prev => ({
      ...prev,
      chunksLoaded: prev.chunksLoaded + 1,
      averageChunkTime: chunkTimesRef.current.reduce((sum, time) => sum + time, 0) / chunkTimesRef.current.length,
    }));
  }, []);

  const handlePageReady = useCallback((event: LoadingEvent) => {
    setStats(prev => ({
      ...prev,
      pagesReady: prev.pagesReady + 1,
    }));
  }, []);

  const handleMetadataLoaded = useCallback((event: LoadingEvent) => {
    setLoadingPhase('processing');
  }, []);

  const handleError = useCallback((event: LoadingEvent) => {
    setError(event.error || new Error('Unknown loading error'));
    setIsLoading(false);
    onError?.(event.error || new Error('Unknown loading error'));
  }, [onError]);

  const handleComplete = useCallback((event: LoadingEvent) => {
    setIsLoading(false);
    setLoadingPhase('complete');
    const document = loaderRef.current?.getDocument();
    if (document) {
      onDocumentLoaded?.(document);
    }
  }, [onDocumentLoaded]);

  const startLoading = useCallback(async () => {
    if (!loaderRef.current || isLoading) return;

    setIsLoading(true);
    setError(null);
    setLoadingPhase('initializing');
    setStats({
      startTime: Date.now(),
      chunksLoaded: 0,
      pagesReady: 0,
      averageChunkTime: 0,
      networkSpeed: '0 KB/s',
      estimatedCompletion: 'Calculating...',
    });
    chunkTimesRef.current = [];
    lastChunkTimeRef.current = Date.now();

    try {
      if (typeof source === 'string') {
        await loaderRef.current.loadFromURL(source);
      } else {
        await loaderRef.current.loadFromFile(source);
      }
    } catch (error) {
      console.error('Loading failed:', error);
    }
  }, [source, isLoading]);

  const cancelLoading = useCallback(() => {
    loaderRef.current?.cancel();
    setIsLoading(false);
    setLoadingPhase('initializing');
    onCancel?.();
  }, [onCancel]);

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const formatTime = (seconds: number): string => {
    if (!seconds || !isFinite(seconds)) return 'Calculating...';
    if (seconds < 60) return `${Math.round(seconds)}s`;
    if (seconds < 3600) return `${Math.round(seconds / 60)}m ${Math.round(seconds % 60)}s`;
    return `${Math.round(seconds / 3600)}h ${Math.round((seconds % 3600) / 60)}m`;
  };

  const getPhaseIcon = () => {
    switch (loadingPhase) {
      case 'initializing':
        return <Clock className="h-4 w-4" />;
      case 'downloading':
        return <Download className="h-4 w-4" />;
      case 'processing':
        return <Zap className="h-4 w-4" />;
      case 'complete':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getPhaseDescription = () => {
    switch (loadingPhase) {
      case 'initializing':
        return 'Preparing to load document...';
      case 'downloading':
        return 'Downloading document chunks...';
      case 'processing':
        return 'Processing document and preloading pages...';
      case 'complete':
        return 'Document loaded successfully!';
      default:
        return 'Ready to load';
    }
  };

  return (
    <Card className={cn("w-full max-w-2xl", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {getPhaseIcon()}
          Progressive PDF Loader
        </CardTitle>
        <CardDescription>
          {getPhaseDescription()}
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error.message}
            </AlertDescription>
          </Alert>
        )}

        {/* Source Information */}
        <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
          <div className="flex items-center gap-3">
            <FileText className="h-5 w-5 text-muted-foreground" />
            <div>
              <div className="font-medium text-sm">
                {typeof source === 'string' ? 'URL' : source.name}
              </div>
              <div className="text-xs text-muted-foreground">
                {typeof source === 'string' ? source : formatBytes(source.size)}
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Badge variant={enableStreaming ? "default" : "secondary"}>
              {enableStreaming ? "Streaming" : "Chunked"}
            </Badge>
            <Badge variant="outline">
              {formatBytes(chunkSize)} chunks
            </Badge>
          </div>
        </div>

        {/* Progress Section */}
        {isLoading && progress && (
          <div className="space-y-4">
            {/* Main Progress Bar */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Overall Progress</span>
                <span>{progress.percentage.toFixed(1)}%</span>
              </div>
              <Progress value={progress.percentage} className="h-2" />
            </div>

            {/* Detailed Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div className="space-y-1">
                <div className="text-lg font-bold">{formatBytes(progress.bytesLoaded)}</div>
                <div className="text-xs text-muted-foreground">Downloaded</div>
              </div>
              <div className="space-y-1">
                <div className="text-lg font-bold">{stats.chunksLoaded}</div>
                <div className="text-xs text-muted-foreground">Chunks</div>
              </div>
              <div className="space-y-1">
                <div className="text-lg font-bold">{stats.pagesReady}</div>
                <div className="text-xs text-muted-foreground">Pages Ready</div>
              </div>
              <div className="space-y-1">
                <div className="text-lg font-bold">{stats.networkSpeed}</div>
                <div className="text-xs text-muted-foreground">Speed</div>
              </div>
            </div>

            {/* Time Estimates */}
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span>ETA: {stats.estimatedCompletion}</span>
              </div>
              <div className="flex items-center gap-2">
                <Wifi className="h-4 w-4 text-muted-foreground" />
                <span>{stats.networkSpeed}</span>
              </div>
            </div>
          </div>
        )}

        {/* Control Buttons */}
        <div className="flex items-center gap-3">
          {!isLoading && loadingPhase !== 'complete' && (
            <Button onClick={startLoading} className="flex-1">
              <Play className="h-4 w-4 mr-2" />
              Start Loading
            </Button>
          )}
          
          {isLoading && (
            <>
              <Button
                variant="outline"
                onClick={() => setIsPaused(!isPaused)}
                disabled={true} // Pause functionality would need implementation
              >
                {isPaused ? <Play className="h-4 w-4 mr-2" /> : <Pause className="h-4 w-4 mr-2" />}
                {isPaused ? 'Resume' : 'Pause'}
              </Button>
              
              <Button variant="destructive" onClick={cancelLoading}>
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
            </>
          )}

          {loadingPhase === 'complete' && (
            <Button variant="outline" onClick={() => setLoadingPhase('initializing')}>
              Load Another
            </Button>
          )}
        </div>

        {/* Technical Details */}
        {isLoading && progress && (
          <details className="text-xs">
            <summary className="cursor-pointer text-muted-foreground hover:text-foreground">
              Technical Details
            </summary>
            <div className="mt-2 p-3 bg-muted rounded space-y-1">
              <div>Total Size: {formatBytes(progress.bytesTotal)}</div>
              <div>Chunk Size: {formatBytes(chunkSize)}</div>
              <div>Total Chunks: {progress.totalChunks}</div>
              <div>Current Chunk: {progress.currentChunk}</div>
              <div>Pages Total: {progress.pagesTotal}</div>
              <div>Average Chunk Time: {stats.averageChunkTime.toFixed(0)}ms</div>
            </div>
          </details>
        )}
      </CardContent>
    </Card>
  );
}
