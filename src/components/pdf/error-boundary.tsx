"use client";

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { 
  AlertTriangle, 
  RefreshCw, 
  Bug, 
  ChevronDown, 
  Copy, 
  Download,
  ExternalLink,
  Shield,
  Zap,
  HelpCircle,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { PDFErrorHandler, type PDFError, type ErrorSeverity } from '@/lib/pdf/error-handler';

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: (error: Error, errorInfo: ErrorInfo) => ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  enableRecovery?: boolean;
  showTechnicalDetails?: boolean;
  className?: string;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  pdfError: PDFError | null;
  isRecovering: boolean;
  recoveryAttempts: number;
  showDetails: boolean;
}

export class PDFErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private errorHandler: PDFErrorHandler;
  private maxRecoveryAttempts = 3;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      pdfError: null,
      isRecovering: false,
      recoveryAttempts: 0,
      showDetails: false,
    };

    this.errorHandler = new PDFErrorHandler({
      enableAutoRecovery: props.enableRecovery ?? true,
      enableUserNotifications: false, // Handle in UI
      logLevel: 'warn',
    });

    this.setupErrorHandlerListeners();
  }

  private setupErrorHandlerListeners(): void {
    this.errorHandler.addEventListener('recovery-success', () => {
      this.setState({ isRecovering: false });
      this.handleRecovery();
    });

    this.errorHandler.addEventListener('recovery-failed', () => {
      this.setState({ isRecovering: false });
    });
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    this.setState({ errorInfo });
    
    // Handle error with PDF error handler
    this.errorHandler.handleError(error, {
      componentStack: errorInfo.componentStack,
      errorBoundary: true,
    }).then((pdfError) => {
      this.setState({ pdfError });
    });

    // Call external error handler if provided
    this.props.onError?.(error, errorInfo);
  }

  private handleRetry = (): void => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      pdfError: null,
      recoveryAttempts: this.state.recoveryAttempts + 1,
    });
  };

  private handleRecovery = (): void => {
    if (this.state.recoveryAttempts < this.maxRecoveryAttempts) {
      this.handleRetry();
    }
  };

  private handleAutoRecover = async (): Promise<void> => {
    if (this.state.isRecovering || !this.state.pdfError) return;

    this.setState({ isRecovering: true });

    try {
      // Attempt automatic recovery
      await this.errorHandler.handleError(this.state.error, {
        autoRecovery: true,
        recoveryAttempt: this.state.recoveryAttempts + 1,
      });
    } catch (recoveryError) {
      console.error('Auto recovery failed:', recoveryError);
      this.setState({ isRecovering: false });
    }
  };

  private copyErrorDetails = (): void => {
    const errorDetails = this.getErrorDetails();
    navigator.clipboard.writeText(errorDetails).then(() => {
      // Could show a toast notification here
    });
  };

  private downloadErrorReport = (): void => {
    const errorReport = this.generateErrorReport();
    const blob = new Blob([errorReport], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `pdf-error-report-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  private getErrorDetails(): string {
    const { error, errorInfo, pdfError } = this.state;
    
    return `
PDF Viewer Error Report
======================

Error: ${error?.message || 'Unknown error'}
Code: ${pdfError?.code || 'UNKNOWN'}
Category: ${pdfError?.category || 'unknown'}
Severity: ${pdfError?.severity || 'unknown'}
Timestamp: ${new Date().toISOString()}

Stack Trace:
${error?.stack || 'No stack trace available'}

Component Stack:
${errorInfo?.componentStack || 'No component stack available'}

Context:
${JSON.stringify(pdfError?.context || {}, null, 2)}

User Agent: ${navigator.userAgent}
URL: ${window.location.href}
    `.trim();
  }

  private generateErrorReport(): string {
    const { error, errorInfo, pdfError } = this.state;
    
    return JSON.stringify({
      timestamp: new Date().toISOString(),
      error: {
        message: error?.message,
        name: error?.name,
        stack: error?.stack,
      },
      pdfError,
      errorInfo: {
        componentStack: errorInfo?.componentStack,
      },
      environment: {
        userAgent: navigator.userAgent,
        url: window.location.href,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight,
        },
        memory: 'memory' in performance ? (performance as any).memory : null,
      },
      metrics: this.errorHandler.getMetrics(),
    }, null, 2);
  }

  private getSeverityColor(severity: ErrorSeverity): string {
    switch (severity) {
      case 'low': return 'text-blue-500';
      case 'medium': return 'text-yellow-500';
      case 'high': return 'text-orange-500';
      case 'critical': return 'text-red-500';
      default: return 'text-gray-500';
    }
  }

  private getSeverityIcon(severity: ErrorSeverity): React.ReactNode {
    switch (severity) {
      case 'low': return <HelpCircle className="h-4 w-4" />;
      case 'medium': return <AlertTriangle className="h-4 w-4" />;
      case 'high': return <Shield className="h-4 w-4" />;
      case 'critical': return <Zap className="h-4 w-4" />;
      default: return <Bug className="h-4 w-4" />;
    }
  }

  render(): ReactNode {
    if (!this.state.hasError) {
      return this.props.children;
    }

    // Use custom fallback if provided
    if (this.props.fallback && this.state.error && this.state.errorInfo) {
      return this.props.fallback(this.state.error, this.state.errorInfo);
    }

    const { error, pdfError, isRecovering, recoveryAttempts, showDetails } = this.state;
    const canRetry = recoveryAttempts < this.maxRecoveryAttempts;
    const canAutoRecover = pdfError?.recoverable && !isRecovering;

    return (
      <div className={cn("flex items-center justify-center min-h-[400px] p-6", this.props.className)}>
        <Card className="w-full max-w-2xl">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {pdfError ? this.getSeverityIcon(pdfError.severity) : <Bug className="h-5 w-5" />}
              <span className={pdfError ? this.getSeverityColor(pdfError.severity) : 'text-red-500'}>
                PDF Viewer Error
              </span>
              {pdfError && (
                <Badge variant="outline" className="ml-auto">
                  {pdfError.severity}
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              {pdfError?.userMessage || error?.message || 'An unexpected error occurred while loading the PDF.'}
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* Error Details */}
            {pdfError && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Error Code:</strong> {pdfError.code}
                  <br />
                  <strong>Category:</strong> {pdfError.category}
                  {pdfError.technicalDetails && (
                    <>
                      <br />
                      <strong>Details:</strong> {pdfError.technicalDetails}
                    </>
                  )}
                </AlertDescription>
              </Alert>
            )}

            {/* Suggested Actions */}
            {pdfError?.suggestedActions && pdfError.suggestedActions.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium">Suggested Actions:</h4>
                <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                  {pdfError.suggestedActions.map((action, index) => (
                    <li key={index}>{action}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-3">
              {canRetry && (
                <Button onClick={this.handleRetry} disabled={isRecovering}>
                  <RefreshCw className={cn("h-4 w-4 mr-2", isRecovering && "animate-spin")} />
                  {isRecovering ? 'Recovering...' : 'Try Again'}
                </Button>
              )}

              {canAutoRecover && (
                <Button variant="outline" onClick={this.handleAutoRecover} disabled={isRecovering}>
                  <Zap className="h-4 w-4 mr-2" />
                  Auto Recover
                </Button>
              )}

              <Button variant="outline" onClick={() => window.location.reload()}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Reload Page
              </Button>

              <Button variant="outline" onClick={this.copyErrorDetails}>
                <Copy className="h-4 w-4 mr-2" />
                Copy Details
              </Button>

              <Button variant="outline" onClick={this.downloadErrorReport}>
                <Download className="h-4 w-4 mr-2" />
                Download Report
              </Button>
            </div>

            {/* Recovery Attempts */}
            {recoveryAttempts > 0 && (
              <div className="text-sm text-muted-foreground">
                Recovery attempts: {recoveryAttempts} / {this.maxRecoveryAttempts}
              </div>
            )}

            {/* Technical Details */}
            {this.props.showTechnicalDetails && (
              <Collapsible open={showDetails} onOpenChange={setShowDetails}>
                <CollapsibleTrigger asChild>
                  <Button variant="ghost" className="w-full justify-between">
                    Technical Details
                    <ChevronDown className={cn("h-4 w-4 transition-transform", showDetails && "rotate-180")} />
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="space-y-4">
                  <div className="p-4 bg-muted rounded-lg">
                    <pre className="text-xs overflow-auto max-h-48">
                      {this.getErrorDetails()}
                    </pre>
                  </div>
                </CollapsibleContent>
              </Collapsible>
            )}

            {/* Help Links */}
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <a
                href="https://github.com/your-repo/issues"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-1 hover:text-foreground"
              >
                <ExternalLink className="h-3 w-3" />
                Report Issue
              </a>
              <a
                href="https://your-docs.com/troubleshooting"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-1 hover:text-foreground"
              >
                <HelpCircle className="h-3 w-3" />
                Troubleshooting Guide
              </a>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  componentWillUnmount(): void {
    this.errorHandler.destroy();
  }
}

// Hook for functional components
export function usePDFErrorHandler() {
  const [errorHandler] = React.useState(() => new PDFErrorHandler());
  
  React.useEffect(() => {
    return () => errorHandler.destroy();
  }, [errorHandler]);

  return errorHandler;
}

// Higher-order component for wrapping components with error boundary
export function withPDFErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) {
  return function WrappedComponent(props: P) {
    return (
      <PDFErrorBoundary {...errorBoundaryProps}>
        <Component {...props} />
      </PDFErrorBoundary>
    );
  };
}
