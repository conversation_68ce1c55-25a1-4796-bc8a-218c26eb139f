"use client";

import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { 
  Search, 
  Filter, 
  SlidersHorizontal, 
  ChevronDown, 
  ChevronUp,
  X,
  Calendar,
  User,
  FileText,
  Tag,
  Brain,
  Zap,
  Eye,
  Download,
  Share,
  Book<PERSON><PERSON>,
  Clock,
  TrendingUp,
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SearchFilters {
  dateRange: {
    start?: Date;
    end?: Date;
  };
  authors: string[];
  documentTypes: string[];
  topics: string[];
  languages: string[];
  pageRange: {
    min?: number;
    max?: number;
  };
  fileSize: {
    min?: number;
    max?: number;
  };
  semanticSimilarity: number;
  includeAnnotations: boolean;
  includeMetadata: boolean;
}

interface SearchFacet {
  name: string;
  values: Array<{
    value: string;
    count: number;
    selected: boolean;
  }>;
}

interface SearchResult {
  id: string;
  title: string;
  content: string;
  author?: string;
  date?: Date;
  type: string;
  pageNumber: number;
  relevanceScore: number;
  semanticScore?: number;
  highlights: string[];
  preview: string;
  metadata: Record<string, any>;
}

interface AdvancedSearchUIProps {
  onSearch: (query: string, filters: SearchFilters) => Promise<SearchResult[]>;
  onSemanticSearch?: (query: string, filters: SearchFilters) => Promise<SearchResult[]>;
  initialQuery?: string;
  className?: string;
}

const DEFAULT_FILTERS: SearchFilters = {
  dateRange: {},
  authors: [],
  documentTypes: [],
  topics: [],
  languages: [],
  pageRange: {},
  fileSize: {},
  semanticSimilarity: 0.7,
  includeAnnotations: true,
  includeMetadata: true,
};

export default function AdvancedSearchUI({
  onSearch,
  onSemanticSearch,
  initialQuery = '',
  className,
}: AdvancedSearchUIProps) {
  const [query, setQuery] = useState(initialQuery);
  const [filters, setFilters] = useState<SearchFilters>(DEFAULT_FILTERS);
  const [results, setResults] = useState<SearchResult[]>([]);
  const [facets, setFacets] = useState<SearchFacet[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchMode, setSearchMode] = useState<'text' | 'semantic'>('text');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedResult, setSelectedResult] = useState<SearchResult | null>(null);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [savedSearches, setSavedSearches] = useState<Array<{ name: string; query: string; filters: SearchFilters }>>([]);

  // Load search history from localStorage
  useEffect(() => {
    const history = localStorage.getItem('pdf-search-history');
    if (history) {
      setSearchHistory(JSON.parse(history));
    }
    
    const saved = localStorage.getItem('pdf-saved-searches');
    if (saved) {
      setSavedSearches(JSON.parse(saved));
    }
  }, []);

  const handleSearch = useCallback(async () => {
    if (!query.trim()) return;

    setIsLoading(true);
    try {
      const searchFn = searchMode === 'semantic' && onSemanticSearch ? onSemanticSearch : onSearch;
      const searchResults = await searchFn(query, filters);
      
      setResults(searchResults);
      generateFacets(searchResults);
      
      // Update search history
      const newHistory = [query, ...searchHistory.filter(h => h !== query)].slice(0, 10);
      setSearchHistory(newHistory);
      localStorage.setItem('pdf-search-history', JSON.stringify(newHistory));
      
    } catch (error) {
      console.error('Search error:', error);
    } finally {
      setIsLoading(false);
    }
  }, [query, filters, searchMode, onSearch, onSemanticSearch, searchHistory]);

  const generateFacets = useCallback((searchResults: SearchResult[]) => {
    const facetData: Record<string, Map<string, number>> = {
      authors: new Map(),
      types: new Map(),
      topics: new Map(),
      languages: new Map(),
    };

    searchResults.forEach(result => {
      if (result.author) {
        facetData.authors.set(result.author, (facetData.authors.get(result.author) || 0) + 1);
      }
      facetData.types.set(result.type, (facetData.types.get(result.type) || 0) + 1);
      
      // Extract topics from metadata
      if (result.metadata.topics) {
        result.metadata.topics.forEach((topic: string) => {
          facetData.topics.set(topic, (facetData.topics.get(topic) || 0) + 1);
        });
      }
      
      if (result.metadata.language) {
        facetData.languages.set(result.metadata.language, (facetData.languages.get(result.metadata.language) || 0) + 1);
      }
    });

    const newFacets: SearchFacet[] = Object.entries(facetData).map(([name, valueMap]) => ({
      name,
      values: Array.from(valueMap.entries())
        .map(([value, count]) => ({
          value,
          count,
          selected: false,
        }))
        .sort((a, b) => b.count - a.count),
    }));

    setFacets(newFacets);
  }, []);

  const updateFilter = useCallback((key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  }, []);

  const toggleFacetValue = useCallback((facetName: string, value: string) => {
    setFacets(prev => prev.map(facet => {
      if (facet.name === facetName) {
        return {
          ...facet,
          values: facet.values.map(v => 
            v.value === value ? { ...v, selected: !v.selected } : v
          ),
        };
      }
      return facet;
    }));

    // Update filters based on facet selection
    const facet = facets.find(f => f.name === facetName);
    if (facet) {
      const selectedValues = facet.values
        .filter(v => v.value === value ? !v.selected : v.selected)
        .map(v => v.value);
      
      switch (facetName) {
        case 'authors':
          updateFilter('authors', selectedValues);
          break;
        case 'types':
          updateFilter('documentTypes', selectedValues);
          break;
        case 'topics':
          updateFilter('topics', selectedValues);
          break;
        case 'languages':
          updateFilter('languages', selectedValues);
          break;
      }
    }
  }, [facets, updateFilter]);

  const clearFilters = useCallback(() => {
    setFilters(DEFAULT_FILTERS);
    setFacets(prev => prev.map(facet => ({
      ...facet,
      values: facet.values.map(v => ({ ...v, selected: false })),
    })));
  }, []);

  const saveSearch = useCallback(() => {
    const name = prompt('Enter a name for this search:');
    if (name) {
      const newSavedSearch = { name, query, filters };
      const updated = [...savedSearches, newSavedSearch];
      setSavedSearches(updated);
      localStorage.setItem('pdf-saved-searches', JSON.stringify(updated));
    }
  }, [query, filters, savedSearches]);

  const loadSavedSearch = useCallback((savedSearch: typeof savedSearches[0]) => {
    setQuery(savedSearch.query);
    setFilters(savedSearch.filters);
  }, []);

  const exportResults = useCallback(() => {
    const exportData = {
      query,
      filters,
      results: results.map(r => ({
        title: r.title,
        content: r.content,
        author: r.author,
        type: r.type,
        pageNumber: r.pageNumber,
        relevanceScore: r.relevanceScore,
      })),
      timestamp: new Date().toISOString(),
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json',
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `search-results-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }, [query, filters, results]);

  const filteredResults = useMemo(() => {
    return results.filter(result => {
      // Apply facet filters
      const authorMatch = filters.authors.length === 0 || 
        (result.author && filters.authors.includes(result.author));
      
      const typeMatch = filters.documentTypes.length === 0 || 
        filters.documentTypes.includes(result.type);
      
      const topicMatch = filters.topics.length === 0 || 
        (result.metadata.topics && 
         result.metadata.topics.some((topic: string) => filters.topics.includes(topic)));

      return authorMatch && typeMatch && topicMatch;
    });
  }, [results, filters]);

  return (
    <div className={cn("space-y-6", className)}>
      {/* Search Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Advanced Search
          </CardTitle>
          <CardDescription>
            Search across documents with advanced filters and semantic understanding
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search Input */}
          <div className="flex gap-2">
            <div className="flex-1 relative">
              <Input
                placeholder="Enter your search query..."
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                className="pr-12"
              />
              {query && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setQuery('')}
                  className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
            
            <Select value={searchMode} onValueChange={(value: 'text' | 'semantic') => setSearchMode(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="text">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Text
                  </div>
                </SelectItem>
                <SelectItem value="semantic">
                  <div className="flex items-center gap-2">
                    <Brain className="h-4 w-4" />
                    Semantic
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
            
            <Button onClick={handleSearch} disabled={isLoading || !query.trim()}>
              {isLoading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
              ) : (
                <Search className="h-4 w-4" />
              )}
            </Button>
            
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>
          </div>

          {/* Search History & Saved Searches */}
          <div className="flex gap-4">
            {searchHistory.length > 0 && (
              <div className="flex-1">
                <Label className="text-sm font-medium">Recent Searches</Label>
                <div className="flex flex-wrap gap-1 mt-1">
                  {searchHistory.slice(0, 5).map((historyQuery, index) => (
                    <Badge
                      key={index}
                      variant="outline"
                      className="cursor-pointer hover:bg-muted"
                      onClick={() => setQuery(historyQuery)}
                    >
                      <Clock className="h-3 w-3 mr-1" />
                      {historyQuery.length > 20 ? historyQuery.slice(0, 20) + '...' : historyQuery}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
            
            {savedSearches.length > 0 && (
              <div className="flex-1">
                <Label className="text-sm font-medium">Saved Searches</Label>
                <div className="flex flex-wrap gap-1 mt-1">
                  {savedSearches.slice(0, 3).map((saved, index) => (
                    <Badge
                      key={index}
                      variant="secondary"
                      className="cursor-pointer hover:bg-secondary/80"
                      onClick={() => loadSavedSearch(saved)}
                    >
                      <BookOpen className="h-3 w-3 mr-1" />
                      {saved.name}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Advanced Filters */}
      <Collapsible open={showFilters} onOpenChange={setShowFilters}>
        <CollapsibleContent>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <SlidersHorizontal className="h-5 w-5" />
                  Advanced Filters
                </span>
                <Button variant="outline" size="sm" onClick={clearFilters}>
                  Clear All
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="basic" className="space-y-4">
                <TabsList>
                  <TabsTrigger value="basic">Basic</TabsTrigger>
                  <TabsTrigger value="content">Content</TabsTrigger>
                  <TabsTrigger value="semantic">Semantic</TabsTrigger>
                </TabsList>
                
                <TabsContent value="basic" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label>Date Range</Label>
                      <div className="flex gap-2 mt-1">
                        <Input
                          type="date"
                          value={filters.dateRange.start?.toISOString().split('T')[0] || ''}
                          onChange={(e) => updateFilter('dateRange', {
                            ...filters.dateRange,
                            start: e.target.value ? new Date(e.target.value) : undefined,
                          })}
                        />
                        <Input
                          type="date"
                          value={filters.dateRange.end?.toISOString().split('T')[0] || ''}
                          onChange={(e) => updateFilter('dateRange', {
                            ...filters.dateRange,
                            end: e.target.value ? new Date(e.target.value) : undefined,
                          })}
                        />
                      </div>
                    </div>
                    
                    <div>
                      <Label>Page Range</Label>
                      <div className="flex gap-2 mt-1">
                        <Input
                          type="number"
                          placeholder="Min"
                          value={filters.pageRange.min || ''}
                          onChange={(e) => updateFilter('pageRange', {
                            ...filters.pageRange,
                            min: e.target.value ? parseInt(e.target.value) : undefined,
                          })}
                        />
                        <Input
                          type="number"
                          placeholder="Max"
                          value={filters.pageRange.max || ''}
                          onChange={(e) => updateFilter('pageRange', {
                            ...filters.pageRange,
                            max: e.target.value ? parseInt(e.target.value) : undefined,
                          })}
                        />
                      </div>
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="content" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center justify-between">
                      <Label>Include Annotations</Label>
                      <Switch
                        checked={filters.includeAnnotations}
                        onCheckedChange={(checked) => updateFilter('includeAnnotations', checked)}
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <Label>Include Metadata</Label>
                      <Switch
                        checked={filters.includeMetadata}
                        onCheckedChange={(checked) => updateFilter('includeMetadata', checked)}
                      />
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="semantic" className="space-y-4">
                  <div>
                    <Label>Semantic Similarity Threshold: {filters.semanticSimilarity}</Label>
                    <Slider
                      value={[filters.semanticSimilarity]}
                      onValueChange={([value]) => updateFilter('semanticSimilarity', value)}
                      min={0}
                      max={1}
                      step={0.1}
                      className="mt-2"
                    />
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </CollapsibleContent>
      </Collapsible>

      {/* Results Section */}
      {results.length > 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Facets */}
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Filter Results</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {facets.map((facet) => (
                  <div key={facet.name}>
                    <Label className="text-sm font-medium capitalize">{facet.name}</Label>
                    <div className="space-y-1 mt-1">
                      {facet.values.slice(0, 5).map((value) => (
                        <div
                          key={value.value}
                          className="flex items-center justify-between cursor-pointer hover:bg-muted/50 p-1 rounded"
                          onClick={() => toggleFacetValue(facet.name, value.value)}
                        >
                          <div className="flex items-center gap-2">
                            <input
                              type="checkbox"
                              checked={value.selected}
                              onChange={() => {}}
                              className="rounded"
                            />
                            <span className="text-sm">{value.value}</span>
                          </div>
                          <Badge variant="outline" className="text-xs">
                            {value.count}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Results List */}
          <div className="lg:col-span-3 space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <h3 className="text-lg font-semibold">
                  {filteredResults.length} Results
                </h3>
                <Badge variant="outline">
                  {searchMode === 'semantic' ? 'Semantic Search' : 'Text Search'}
                </Badge>
              </div>
              
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={saveSearch}>
                  <BookOpen className="h-4 w-4 mr-2" />
                  Save Search
                </Button>
                <Button variant="outline" size="sm" onClick={exportResults}>
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </div>
            </div>

            {filteredResults.map((result) => (
              <Card
                key={result.id}
                className={cn(
                  "cursor-pointer transition-colors hover:bg-muted/50",
                  selectedResult?.id === result.id && "ring-2 ring-primary"
                )}
                onClick={() => setSelectedResult(result)}
              >
                <CardContent className="p-4">
                  <div className="space-y-2">
                    <div className="flex items-start justify-between">
                      <h4 className="font-medium">{result.title}</h4>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{result.type}</Badge>
                        <Badge variant="secondary">
                          <TrendingUp className="h-3 w-3 mr-1" />
                          {Math.round(result.relevanceScore * 100)}%
                        </Badge>
                      </div>
                    </div>
                    
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {result.preview}
                    </p>
                    
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      {result.author && (
                        <span className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          {result.author}
                        </span>
                      )}
                      <span className="flex items-center gap-1">
                        <FileText className="h-3 w-3" />
                        Page {result.pageNumber}
                      </span>
                      {result.date && (
                        <span className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {result.date.toLocaleDateString()}
                        </span>
                      )}
                    </div>
                    
                    {result.highlights.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {result.highlights.slice(0, 3).map((highlight, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {highlight}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
