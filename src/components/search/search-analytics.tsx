"use client";

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  BarChart3, 
  TrendingUp, 
  Clock, 
  Search, 
  Target, 
  Users, 
  FileText, 
  Tag,
  Calendar,
  Zap,
  Eye,
  Download,
  Share,
  Filter,
  Hash,
  Activity,
  PieChart,
  LineChart
} from 'lucide-react';
import { cn } from '@/lib/utils';

export interface SearchAnalyticsData {
  totalSearches: number;
  uniqueQueries: number;
  averageResultsPerSearch: number;
  searchSuccessRate: number;
  topQueries: Array<{
    query: string;
    count: number;
    successRate: number;
    avgResults: number;
  }>;
  recentQueries: Array<{
    query: string;
    timestamp: Date;
    resultCount: number;
    clickedResults: number;
  }>;
  searchTrends: Array<{
    date: Date;
    searches: number;
    successfulSearches: number;
  }>;
  contentTypeDistribution: Array<{
    type: string;
    count: number;
    percentage: number;
  }>;
  userBehavior: {
    avgSearchLength: number;
    avgTimeToClick: number;
    mostActiveHours: Array<{ hour: number; count: number }>;
    searchPatterns: Array<{
      pattern: string;
      frequency: number;
      description: string;
    }>;
  };
  performanceMetrics: {
    avgSearchTime: number;
    avgRenderTime: number;
    cacheHitRate: number;
    errorRate: number;
  };
}

interface SearchAnalyticsProps {
  data: SearchAnalyticsData;
  onExportData?: () => void;
  onClearData?: () => void;
  className?: string;
}

export default function SearchAnalytics({
  data,
  onExportData,
  onClearData,
  className,
}: SearchAnalyticsProps) {
  const [selectedTimeRange, setSelectedTimeRange] = useState<'day' | 'week' | 'month' | 'year'>('week');

  // Calculate insights
  const insights = useMemo(() => {
    const searchGrowth = data.searchTrends.length > 1 
      ? ((data.searchTrends[data.searchTrends.length - 1]?.searches || 0) - 
         (data.searchTrends[0]?.searches || 0)) / (data.searchTrends[0]?.searches || 1) * 100
      : 0;

    const topContentType = data.contentTypeDistribution.reduce((max, current) => 
      current.count > max.count ? current : max, data.contentTypeDistribution[0] || { type: 'text', count: 0, percentage: 0 }
    );

    const peakHour = data.userBehavior.mostActiveHours.reduce((max, current) => 
      current.count > max.count ? current : max, data.userBehavior.mostActiveHours[0] || { hour: 12, count: 0 }
    );

    return {
      searchGrowth,
      topContentType,
      peakHour,
      efficiency: data.searchSuccessRate * data.performanceMetrics.cacheHitRate,
    };
  }, [data]);

  const renderOverviewTab = () => (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Searches</CardTitle>
            <Search className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalSearches.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {insights.searchGrowth > 0 ? '+' : ''}{insights.searchGrowth.toFixed(1)}% from last period
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{(data.searchSuccessRate * 100).toFixed(1)}%</div>
            <Progress value={data.searchSuccessRate * 100} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Results</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.averageResultsPerSearch.toFixed(1)}</div>
            <p className="text-xs text-muted-foreground">
              Per search query
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cache Hit Rate</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{(data.performanceMetrics.cacheHitRate * 100).toFixed(1)}%</div>
            <Progress value={data.performanceMetrics.cacheHitRate * 100} className="mt-2" />
          </CardContent>
        </Card>
      </div>

      {/* Search Trends */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Search Trends
          </CardTitle>
          <CardDescription>
            Search activity over time
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Simplified trend visualization */}
            <div className="grid grid-cols-7 gap-2">
              {data.searchTrends.slice(-7).map((trend, index) => (
                <div key={index} className="text-center">
                  <div className="text-xs text-muted-foreground mb-1">
                    {trend.date.toLocaleDateString('en', { weekday: 'short' })}
                  </div>
                  <div 
                    className="bg-primary rounded-sm mx-auto"
                    style={{ 
                      height: `${Math.max(4, (trend.searches / Math.max(...data.searchTrends.map(t => t.searches))) * 60)}px`,
                      width: '20px'
                    }}
                  />
                  <div className="text-xs font-medium mt-1">
                    {trend.searches}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Top Queries */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Hash className="h-5 w-5" />
            Top Search Queries
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-64">
            <div className="space-y-2">
              {data.topQueries.slice(0, 10).map((query, index) => (
                <div key={query.query} className="flex items-center justify-between p-2 rounded-lg hover:bg-muted">
                  <div className="flex items-center gap-3">
                    <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">
                      {index + 1}
                    </Badge>
                    <div>
                      <div className="font-medium text-sm">{query.query}</div>
                      <div className="text-xs text-muted-foreground">
                        {query.count} searches • {(query.successRate * 100).toFixed(0)}% success
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">{query.avgResults.toFixed(1)}</div>
                    <div className="text-xs text-muted-foreground">avg results</div>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );

  const renderBehaviorTab = () => (
    <div className="space-y-6">
      {/* User Behavior Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Avg Search Length</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.userBehavior.avgSearchLength.toFixed(1)}</div>
            <p className="text-xs text-muted-foreground">characters per query</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Time to Click</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{(data.userBehavior.avgTimeToClick / 1000).toFixed(1)}s</div>
            <p className="text-xs text-muted-foreground">average time to first click</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Peak Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{insights.peakHour.hour}:00</div>
            <p className="text-xs text-muted-foreground">{insights.peakHour.count} searches</p>
          </CardContent>
        </Card>
      </div>

      {/* Activity by Hour */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Activity by Hour
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-12 gap-1">
            {Array.from({ length: 24 }, (_, hour) => {
              const activity = data.userBehavior.mostActiveHours.find(h => h.hour === hour);
              const count = activity?.count || 0;
              const maxCount = Math.max(...data.userBehavior.mostActiveHours.map(h => h.count));
              const intensity = maxCount > 0 ? count / maxCount : 0;
              
              return (
                <div key={hour} className="text-center">
                  <div className="text-xs text-muted-foreground mb-1">
                    {hour.toString().padStart(2, '0')}
                  </div>
                  <div 
                    className={cn(
                      "rounded-sm mx-auto transition-colors",
                      intensity > 0.7 ? "bg-primary" :
                      intensity > 0.4 ? "bg-primary/60" :
                      intensity > 0.1 ? "bg-primary/30" : "bg-muted"
                    )}
                    style={{ height: '40px', width: '100%' }}
                    title={`${hour}:00 - ${count} searches`}
                  />
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Search Patterns */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Search Patterns
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {data.userBehavior.searchPatterns.map((pattern, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <div className="font-medium text-sm">{pattern.pattern}</div>
                  <div className="text-xs text-muted-foreground">{pattern.description}</div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium">{pattern.frequency}</div>
                  <div className="text-xs text-muted-foreground">occurrences</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderContentTab = () => (
    <div className="space-y-6">
      {/* Content Type Distribution */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PieChart className="h-5 w-5" />
            Content Type Distribution
          </CardTitle>
          <CardDescription>
            What users search for most
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.contentTypeDistribution.map((type, index) => (
              <div key={type.type} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div 
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: `hsl(${index * 45}, 70%, 50%)` }}
                    />
                    <span className="text-sm font-medium capitalize">{type.type}</span>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {type.count} ({type.percentage.toFixed(1)}%)
                  </div>
                </div>
                <Progress value={type.percentage} className="h-2" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Search Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Recent Search Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-64">
            <div className="space-y-2">
              {data.recentQueries.slice(0, 20).map((query, index) => (
                <div key={index} className="flex items-center justify-between p-2 rounded-lg hover:bg-muted">
                  <div>
                    <div className="font-medium text-sm">{query.query}</div>
                    <div className="text-xs text-muted-foreground">
                      {query.timestamp.toLocaleString()}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm">{query.resultCount} results</div>
                    <div className="text-xs text-muted-foreground">
                      {query.clickedResults} clicked
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );

  const renderPerformanceTab = () => (
    <div className="space-y-6">
      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Search Time</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.performanceMetrics.avgSearchTime}ms</div>
            <p className="text-xs text-muted-foreground">average search time</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Render Time</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.performanceMetrics.avgRenderTime}ms</div>
            <p className="text-xs text-muted-foreground">average render time</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Cache Hit Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{(data.performanceMetrics.cacheHitRate * 100).toFixed(1)}%</div>
            <Progress value={data.performanceMetrics.cacheHitRate * 100} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Error Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{(data.performanceMetrics.errorRate * 100).toFixed(2)}%</div>
            <Progress 
              value={data.performanceMetrics.errorRate * 100} 
              className="mt-2"
              // Use red color for error rate
            />
          </CardContent>
        </Card>
      </div>

      {/* Performance Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Performance Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 border rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <TrendingUp className="h-4 w-4 text-green-500" />
                <span className="font-medium text-sm">Search Efficiency</span>
              </div>
              <div className="text-2xl font-bold mb-1">{insights.efficiency.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">
                Combined success rate and cache performance
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-3 border rounded-lg">
                <div className="text-sm font-medium mb-1">Most Searched Content</div>
                <div className="text-lg font-bold">{insights.topContentType.type}</div>
                <div className="text-xs text-muted-foreground">
                  {insights.topContentType.percentage.toFixed(1)}% of searches
                </div>
              </div>

              <div className="p-3 border rounded-lg">
                <div className="text-sm font-medium mb-1">Peak Usage</div>
                <div className="text-lg font-bold">{insights.peakHour.hour}:00</div>
                <div className="text-xs text-muted-foreground">
                  {insights.peakHour.count} searches per hour
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-6 w-6" />
                Search Analytics
              </CardTitle>
              <CardDescription>
                Insights into search behavior and performance
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              {onExportData && (
                <Button variant="outline" size="sm" onClick={onExportData}>
                  <Download className="h-3 w-3 mr-1" />
                  Export
                </Button>
              )}
              {onClearData && (
                <Button variant="outline" size="sm" onClick={onClearData}>
                  Clear Data
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Analytics Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="behavior">Behavior</TabsTrigger>
          <TabsTrigger value="content">Content</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          {renderOverviewTab()}
        </TabsContent>

        <TabsContent value="behavior">
          {renderBehaviorTab()}
        </TabsContent>

        <TabsContent value="content">
          {renderContentTab()}
        </TabsContent>

        <TabsContent value="performance">
          {renderPerformanceTab()}
        </TabsContent>
      </Tabs>
    </div>
  );
}
