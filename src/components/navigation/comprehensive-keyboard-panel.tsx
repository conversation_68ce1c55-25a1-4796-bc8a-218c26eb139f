"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Keyboard, 
  Settings, 
  Plus, 
  Edit, 
  Trash2, 
  Save,
  RotateCcw,
  HelpCircle,
  Zap,
  Eye,
  Search,
  Navigation,
  Accessibility,
  MousePointer,
  Command,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { 
  ComprehensiveKeyboardNavigation, 
  type KeyboardShortcut,
  type KeyboardNavigationConfig,
  type NavigationState,
} from '@/lib/navigation/comprehensive-keyboard-navigation';

interface ComprehensiveKeyboardPanelProps {
  keyboardNavigation: ComprehensiveKeyboardNavigation;
  className?: string;
}

export default function ComprehensiveKeyboardPanel({
  keyboardNavigation,
  className,
}: ComprehensiveKeyboardPanelProps) {
  const [shortcuts, setShortcuts] = useState<KeyboardShortcut[]>([]);
  const [navigationState, setNavigationState] = useState<NavigationState | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [editingShortcut, setEditingShortcut] = useState<KeyboardShortcut | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newShortcut, setNewShortcut] = useState({
    name: '',
    description: '',
    keys: '',
    category: 'general' as KeyboardShortcut['category'],
  });

  // Load data on mount
  useEffect(() => {
    updateData();
  }, []);

  // Listen for keyboard navigation events
  useEffect(() => {
    const handleShortcutExecuted = () => updateData();
    const handleContextChanged = () => updateData();
    const handleShortcutRegistered = () => updateData();
    const handleShortcutUpdated = () => updateData();

    keyboardNavigation.addEventListener('shortcut-executed', handleShortcutExecuted);
    keyboardNavigation.addEventListener('context-changed', handleContextChanged);
    keyboardNavigation.addEventListener('shortcut-registered', handleShortcutRegistered);
    keyboardNavigation.addEventListener('shortcut-updated', handleShortcutUpdated);

    return () => {
      keyboardNavigation.removeEventListener('shortcut-executed', handleShortcutExecuted);
      keyboardNavigation.removeEventListener('context-changed', handleContextChanged);
      keyboardNavigation.removeEventListener('shortcut-registered', handleShortcutRegistered);
      keyboardNavigation.removeEventListener('shortcut-updated', handleShortcutUpdated);
    };
  }, [keyboardNavigation]);

  const updateData = useCallback(() => {
    setShortcuts(keyboardNavigation.getShortcuts());
    setNavigationState(keyboardNavigation.getNavigationState());
  }, [keyboardNavigation]);

  const handleCreateShortcut = useCallback(() => {
    if (!newShortcut.name.trim() || !newShortcut.keys.trim()) return;

    const keys = newShortcut.keys.split(',').map(k => k.trim());
    
    keyboardNavigation.registerShortcut({
      name: newShortcut.name,
      description: newShortcut.description,
      keys,
      category: newShortcut.category,
      enabled: true,
      customizable: true,
      action: () => {
        console.log(`Custom shortcut executed: ${newShortcut.name}`);
      },
    });

    setNewShortcut({
      name: '',
      description: '',
      keys: '',
      category: 'general',
    });
    setShowCreateDialog(false);
  }, [keyboardNavigation, newShortcut]);

  const handleUpdateShortcut = useCallback((shortcut: KeyboardShortcut, updates: Partial<KeyboardShortcut>) => {
    keyboardNavigation.updateShortcut(shortcut.id, updates);
    setEditingShortcut(null);
  }, [keyboardNavigation]);

  const handleDeleteShortcut = useCallback((shortcutId: string) => {
    keyboardNavigation.unregisterShortcut(shortcutId);
  }, [keyboardNavigation]);

  const filteredShortcuts = shortcuts.filter(shortcut => {
    const matchesCategory = selectedCategory === 'all' || shortcut.category === selectedCategory;
    const matchesSearch = !searchQuery || 
      shortcut.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      shortcut.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      shortcut.keys.some(key => key.toLowerCase().includes(searchQuery.toLowerCase()));
    
    return matchesCategory && matchesSearch;
  });

  const categories = [
    { id: 'all', name: 'All', icon: Command },
    { id: 'navigation', name: 'Navigation', icon: Navigation },
    { id: 'zoom', name: 'Zoom', icon: Eye },
    { id: 'view', name: 'View', icon: MousePointer },
    { id: 'search', name: 'Search', icon: Search },
    { id: 'accessibility', name: 'Accessibility', icon: Accessibility },
    { id: 'general', name: 'General', icon: Settings },
  ];

  const getCategoryIcon = (category: string) => {
    const categoryData = categories.find(c => c.id === category);
    return categoryData ? categoryData.icon : Command;
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      navigation: 'text-blue-600',
      zoom: 'text-green-600',
      view: 'text-purple-600',
      search: 'text-orange-600',
      accessibility: 'text-red-600',
      general: 'text-gray-600',
    };
    return colors[category as keyof typeof colors] || 'text-gray-600';
  };

  return (
    <div className={cn("space-y-4", className)}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Keyboard className="h-5 w-5" />
            Comprehensive Keyboard Navigation
          </CardTitle>
          <CardDescription>
            Advanced keyboard shortcuts and navigation patterns
          </CardDescription>
        </CardHeader>
      </Card>

      <Tabs defaultValue="shortcuts" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="shortcuts">Shortcuts</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
          <TabsTrigger value="help">Help</TabsTrigger>
        </TabsList>

        <TabsContent value="shortcuts" className="space-y-4">
          {/* Controls */}
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search shortcuts..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="max-w-sm"
              />
            </div>
            
            <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Shortcut
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create Custom Shortcut</DialogTitle>
                  <DialogDescription>
                    Define a new keyboard shortcut for custom actions
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="shortcut-name">Name</Label>
                    <Input
                      id="shortcut-name"
                      value={newShortcut.name}
                      onChange={(e) => setNewShortcut(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Enter shortcut name"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="shortcut-description">Description</Label>
                    <Input
                      id="shortcut-description"
                      value={newShortcut.description}
                      onChange={(e) => setNewShortcut(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Enter shortcut description"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="shortcut-keys">Keys (comma-separated)</Label>
                    <Input
                      id="shortcut-keys"
                      value={newShortcut.keys}
                      onChange={(e) => setNewShortcut(prev => ({ ...prev, keys: e.target.value }))}
                      placeholder="Ctrl+Shift+X, Alt+X"
                    />
                  </div>
                  
                  <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleCreateShortcut}>
                      Create Shortcut
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          {/* Category Filter */}
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => {
              const Icon = category.icon;
              const isSelected = selectedCategory === category.id;
              
              return (
                <Button
                  key={category.id}
                  variant={isSelected ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category.id)}
                  className="flex items-center gap-2"
                >
                  <Icon className="h-4 w-4" />
                  {category.name}
                </Button>
              );
            })}
          </div>

          {/* Shortcuts List */}
          <ScrollArea className="h-96">
            <div className="space-y-2">
              {filteredShortcuts.map((shortcut) => {
                const Icon = getCategoryIcon(shortcut.category);
                const isEditing = editingShortcut?.id === shortcut.id;
                
                return (
                  <Card key={shortcut.id} className="hover:bg-muted/50">
                    <CardContent className="p-4">
                      {isEditing ? (
                        <div className="space-y-3">
                          <Input
                            value={editingShortcut.name}
                            onChange={(e) => setEditingShortcut(prev => prev ? { ...prev, name: e.target.value } : null)}
                            placeholder="Shortcut name"
                          />
                          <Input
                            value={editingShortcut.description}
                            onChange={(e) => setEditingShortcut(prev => prev ? { ...prev, description: e.target.value } : null)}
                            placeholder="Shortcut description"
                          />
                          <Input
                            value={editingShortcut.keys.join(', ')}
                            onChange={(e) => setEditingShortcut(prev => prev ? { 
                              ...prev, 
                              keys: e.target.value.split(',').map(k => k.trim()) 
                            } : null)}
                            placeholder="Keys (comma-separated)"
                          />
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setEditingShortcut(null)}
                            >
                              Cancel
                            </Button>
                            <Button
                              size="sm"
                              onClick={() => handleUpdateShortcut(shortcut, editingShortcut)}
                            >
                              <Save className="h-4 w-4 mr-2" />
                              Save
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-2">
                          <div className="flex items-start justify-between">
                            <div className="flex items-start gap-3">
                              <div className={cn("p-1 mt-1", getCategoryColor(shortcut.category))}>
                                <Icon className="h-4 w-4" />
                              </div>
                              <div className="flex-1">
                                <div className="font-medium">{shortcut.name}</div>
                                <div className="text-sm text-muted-foreground">
                                  {shortcut.description}
                                </div>
                                <div className="flex flex-wrap gap-1 mt-2">
                                  {shortcut.keys.map((key, index) => (
                                    <Badge key={index} variant="outline" className="text-xs font-mono">
                                      {key}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            </div>
                            
                            <div className="flex items-center gap-2">
                              <Switch
                                checked={shortcut.enabled}
                                onCheckedChange={(checked) => handleUpdateShortcut(shortcut, { enabled: checked })}
                                disabled={!shortcut.customizable}
                              />
                              
                              {shortcut.customizable && (
                                <>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => setEditingShortcut(shortcut)}
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleDeleteShortcut(shortcut.id)}
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </>
                              )}
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <Badge variant="secondary" className="text-xs">
                              {shortcut.category}
                            </Badge>
                            {shortcut.context && (
                              <Badge variant="outline" className="text-xs">
                                {shortcut.context}
                              </Badge>
                            )}
                            {!shortcut.customizable && (
                              <Badge variant="outline" className="text-xs">
                                System
                              </Badge>
                            )}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                );
              })}
              
              {filteredShortcuts.length === 0 && (
                <div className="text-center text-muted-foreground py-8">
                  No shortcuts found matching your criteria
                </div>
              )}
            </div>
          </ScrollArea>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Navigation Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="global-shortcuts">Global Shortcuts</Label>
                  <Switch id="global-shortcuts" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="contextual-shortcuts">Contextual Shortcuts</Label>
                  <Switch id="contextual-shortcuts" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="custom-shortcuts">Custom Shortcuts</Label>
                  <Switch id="custom-shortcuts" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="help-overlay">Help Overlay</Label>
                  <Switch id="help-overlay" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="focus-management">Focus Management</Label>
                  <Switch id="focus-management" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="haptic-feedback">Haptic Feedback</Label>
                  <Switch id="haptic-feedback" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Current State */}
          {navigationState && (
            <Card>
              <CardHeader>
                <CardTitle>Current State</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Context:</span> {navigationState.currentContext}
                  </div>
                  <div>
                    <span className="font-medium">Active Shortcuts:</span> {navigationState.activeShortcuts.size}
                  </div>
                  <div>
                    <span className="font-medium">Focus History:</span> {navigationState.focusHistory.length}
                  </div>
                  <div>
                    <span className="font-medium">Key Sequence:</span> {navigationState.keySequence.join(' ')}
                  </div>
                </div>
                
                {navigationState.focusedElement && (
                  <div>
                    <span className="font-medium">Focused Element:</span>
                    <div className="text-xs text-muted-foreground mt-1">
                      {navigationState.focusedElement.tagName.toLowerCase()}
                      {navigationState.focusedElement.className && ` .${navigationState.focusedElement.className}`}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="help" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Quick Help</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button
                onClick={() => keyboardNavigation.showHelpOverlay()}
                className="w-full"
              >
                <HelpCircle className="h-4 w-4 mr-2" />
                Show Full Keyboard Shortcuts Help
              </Button>
              
              <div className="space-y-3 text-sm">
                <div>
                  <div className="font-medium">Most Used Shortcuts:</div>
                  <div className="space-y-1 mt-2">
                    <div className="flex justify-between">
                      <span>Search Document</span>
                      <Badge variant="outline" className="font-mono">Ctrl+F</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Next Page</span>
                      <Badge variant="outline" className="font-mono">→</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Zoom In</span>
                      <Badge variant="outline" className="font-mono">Ctrl++</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Show Help</span>
                      <Badge variant="outline" className="font-mono">?</Badge>
                    </div>
                  </div>
                </div>
                
                <div>
                  <div className="font-medium">Tips:</div>
                  <ul className="list-disc list-inside space-y-1 mt-2 text-muted-foreground">
                    <li>Hold Shift to reverse direction for navigation shortcuts</li>
                    <li>Use Alt + letter keys for accessibility navigation</li>
                    <li>Escape key cancels most actions and closes dialogs</li>
                    <li>Custom shortcuts are saved automatically</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
