import type { ComparisonResult, ComparisonOptions } from './pdf-comparison-viewer';
import type { DocumentInstance } from '@/lib/types/pdf';

export interface ComparisonExportData {
  metadata: {
    exportDate: string;
    exportFormat: string;
    comparisonId: string;
  };
  documents: {
    document1: {
      id: string;
      title: string;
      pages: number;
      metadata: any;
    };
    document2: {
      id: string;
      title: string;
      pages: number;
      metadata: any;
    };
  };
  options: ComparisonOptions;
  results: ComparisonResult[];
  summary: {
    totalPages: number;
    pagesWithDifferences: number;
    averageSimilarity: number;
    totalDifferences: number;
    addedContent: number;
    removedContent: number;
    modifiedContent: number;
  };
}

export class ComparisonExporter {
  static generateExportData(
    document1: DocumentInstance,
    document2: DocumentInstance,
    results: ComparisonResult[],
    options: ComparisonOptions
  ): ComparisonExportData {
    const summary = this.calculateSummary(results);
    
    return {
      metadata: {
        exportDate: new Date().toISOString(),
        exportFormat: 'json',
        comparisonId: `comparison_${document1.id}_${document2.id}_${Date.now()}`
      },
      documents: {
        document1: {
          id: document1.id,
          title: document1.title,
          pages: document1.numPages,
          metadata: document1.metadata
        },
        document2: {
          id: document2.id,
          title: document2.title,
          pages: document2.numPages,
          metadata: document2.metadata
        }
      },
      options,
      results,
      summary
    };
  }

  static async exportAsJSON(
    document1: DocumentInstance,
    document2: DocumentInstance,
    results: ComparisonResult[],
    options: ComparisonOptions
  ): Promise<void> {
    const exportData = this.generateExportData(document1, document2, results, options);
    exportData.metadata.exportFormat = 'json';
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    
    this.downloadFile(blob, `comparison-${Date.now()}.json`);
  }

  static async exportAsHTML(
    document1: DocumentInstance,
    document2: DocumentInstance,
    results: ComparisonResult[],
    options: ComparisonOptions
  ): Promise<void> {
    const exportData = this.generateExportData(document1, document2, results, options);
    const html = this.generateHTMLReport(exportData);
    
    const blob = new Blob([html], {
      type: 'text/html'
    });
    
    this.downloadFile(blob, `comparison-${Date.now()}.html`);
  }

  static async exportAsCSV(
    document1: DocumentInstance,
    document2: DocumentInstance,
    results: ComparisonResult[],
    options: ComparisonOptions
  ): Promise<void> {
    const exportData = this.generateExportData(document1, document2, results, options);
    const csv = this.generateCSVReport(exportData);
    
    const blob = new Blob([csv], {
      type: 'text/csv'
    });
    
    this.downloadFile(blob, `comparison-${Date.now()}.csv`);
  }

  private static calculateSummary(results: ComparisonResult[]) {
    const totalPages = results.length;
    const pagesWithDifferences = results.filter(r => r.differences.length > 0).length;
    const averageSimilarity = results.reduce((sum, r) => sum + r.similarity, 0) / totalPages;
    
    let totalDifferences = 0;
    let addedContent = 0;
    let removedContent = 0;
    let modifiedContent = 0;

    results.forEach(result => {
      totalDifferences += result.differences.length;
      result.differences.forEach(diff => {
        switch (diff.type) {
          case 'added':
            addedContent++;
            break;
          case 'removed':
            removedContent++;
            break;
          case 'modified':
            modifiedContent++;
            break;
        }
      });
    });

    return {
      totalPages,
      pagesWithDifferences,
      averageSimilarity,
      totalDifferences,
      addedContent,
      removedContent,
      modifiedContent
    };
  }

  private static generateHTMLReport(exportData: ComparisonExportData): string {
    const { documents, summary, results, metadata } = exportData;
    
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Comparison Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .header { border-bottom: 2px solid #333; padding-bottom: 10px; margin-bottom: 20px; }
        .summary { background: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .documents { display: flex; gap: 20px; margin-bottom: 20px; }
        .document { flex: 1; border: 1px solid #ddd; padding: 15px; border-radius: 5px; }
        .results { margin-top: 20px; }
        .page-result { border: 1px solid #ddd; margin-bottom: 10px; padding: 10px; border-radius: 5px; }
        .similarity { font-weight: bold; color: ${summary.averageSimilarity > 0.8 ? '#22c55e' : summary.averageSimilarity > 0.5 ? '#f59e0b' : '#ef4444'}; }
        .diff-added { background: #dcfce7; color: #166534; padding: 2px 4px; border-radius: 3px; }
        .diff-removed { background: #fecaca; color: #991b1b; padding: 2px 4px; border-radius: 3px; }
        .diff-modified { background: #fef3c7; color: #92400e; padding: 2px 4px; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Document Comparison Report</h1>
        <p>Generated on: ${new Date(metadata.exportDate).toLocaleString()}</p>
        <p>Comparison ID: ${metadata.comparisonId}</p>
    </div>

    <div class="summary">
        <h2>Summary</h2>
        <ul>
            <li>Total Pages: ${summary.totalPages}</li>
            <li>Pages with Differences: ${summary.pagesWithDifferences}</li>
            <li>Average Similarity: <span class="similarity">${Math.round(summary.averageSimilarity * 100)}%</span></li>
            <li>Total Differences: ${summary.totalDifferences}</li>
            <li>Added Content: <span class="diff-added">${summary.addedContent}</span></li>
            <li>Removed Content: <span class="diff-removed">${summary.removedContent}</span></li>
            <li>Modified Content: <span class="diff-modified">${summary.modifiedContent}</span></li>
        </ul>
    </div>

    <div class="documents">
        <div class="document">
            <h3>Document 1</h3>
            <p><strong>Title:</strong> ${documents.document1.title}</p>
            <p><strong>Pages:</strong> ${documents.document1.pages}</p>
            <p><strong>Author:</strong> ${documents.document1.metadata.author || 'Unknown'}</p>
        </div>
        <div class="document">
            <h3>Document 2</h3>
            <p><strong>Title:</strong> ${documents.document2.title}</p>
            <p><strong>Pages:</strong> ${documents.document2.pages}</p>
            <p><strong>Author:</strong> ${documents.document2.metadata.author || 'Unknown'}</p>
        </div>
    </div>

    <div class="results">
        <h2>Page-by-Page Results</h2>
        ${results.map(result => `
            <div class="page-result">
                <h4>Page ${result.pageNumber}</h4>
                <p>Similarity: <span class="similarity">${Math.round(result.similarity * 100)}%</span></p>
                <p>Differences: ${result.differences.length}</p>
                ${result.differences.length > 0 ? `
                    <ul>
                        ${result.differences.map(diff => `
                            <li class="diff-${diff.type}">${diff.type}: ${diff.content.substring(0, 100)}${diff.content.length > 100 ? '...' : ''}</li>
                        `).join('')}
                    </ul>
                ` : '<p>No differences found on this page.</p>'}
            </div>
        `).join('')}
    </div>
</body>
</html>`;
  }

  private static generateCSVReport(exportData: ComparisonExportData): string {
    const { results } = exportData;
    
    let csv = 'Page,Similarity,Differences,Added,Removed,Modified\n';
    
    results.forEach(result => {
      const added = result.differences.filter(d => d.type === 'added').length;
      const removed = result.differences.filter(d => d.type === 'removed').length;
      const modified = result.differences.filter(d => d.type === 'modified').length;
      
      csv += `${result.pageNumber},${result.similarity.toFixed(3)},${result.differences.length},${added},${removed},${modified}\n`;
    });
    
    return csv;
  }

  private static downloadFile(blob: Blob, filename: string): void {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
}
