"use client";

import React, { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Layout,
  Monitor,
  Smartphone,
  Tablet,
  Grid3X3,
  Split,
  Layers,
  Maximize2,
  Minimize2,
  Sidebar,
  Settings,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Import our enhanced components for demonstration
import CompactHeader from '../navigation/compact-header';
import PDFSidebar from '../navigation/pdf-sidebar';
import DocumentTabs from '../navigation/document-tabs';
import MultiPDFLayoutManager from '../layout/multi-pdf-layout-manager';
import ResponsiveLayoutManager from '../layout/responsive-layout-manager';

interface LayoutOptimizationDemoProps {
  className?: string;
}

const DEMO_DOCUMENTS = [
  { id: '1', title: 'Document 1.pdf', file: '', pageNumber: 1, numPages: 10, scale: 1.0, isLoading: false, hasError: false },
  { id: '2', title: 'Report 2024.pdf', file: '', pageNumber: 1, numPages: 25, scale: 1.0, isLoading: false, hasError: false },
  { id: '3', title: 'Presentation.pdf', file: '', pageNumber: 1, numPages: 15, scale: 1.0, isLoading: false, hasError: false },
  { id: '4', title: 'Manual.pdf', file: '', pageNumber: 1, numPages: 50, scale: 1.0, isLoading: false, hasError: false },
];

export default function LayoutOptimizationDemo({ className }: LayoutOptimizationDemoProps) {
  const [activeDemo, setActiveDemo] = useState<'before' | 'after'>('after');
  const [viewportSize, setViewportSize] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');
  const [layoutMode, setLayoutMode] = useState<'single' | 'side-by-side' | 'grid'>('single');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [activeDocument, setActiveDocument] = useState('1');

  const optimizations = [
    {
      title: 'Compact Header',
      description: 'Reduced header height by 40% while maintaining functionality',
      improvement: '40% space saved',
      status: 'implemented'
    },
    {
      title: 'Optimized Sidebar',
      description: 'Collapsible sidebar with mini, compact, and full modes',
      improvement: '60% space saved in mini mode',
      status: 'implemented'
    },
    {
      title: 'Enhanced Document Tabs',
      description: 'Compact tabs with smart overflow handling and pinning',
      improvement: '30% more tabs visible',
      status: 'implemented'
    },
    {
      title: 'Multi-PDF Layout',
      description: 'Side-by-side, grid, and overlay viewing modes',
      improvement: 'Up to 4 PDFs simultaneously',
      status: 'implemented'
    },
    {
      title: 'Responsive Design',
      description: 'Adaptive layout based on screen size and orientation',
      improvement: 'Optimized for all devices',
      status: 'implemented'
    },
    {
      title: 'Smart Spacing',
      description: 'Reduced margins and padding throughout the interface',
      improvement: '25% more content area',
      status: 'implemented'
    }
  ];

  const getViewportDimensions = () => {
    switch (viewportSize) {
      case 'mobile':
        return { width: '375px', height: '667px' };
      case 'tablet':
        return { width: '768px', height: '1024px' };
      case 'desktop':
        return { width: '1440px', height: '900px' };
      default:
        return { width: '100%', height: '600px' };
    }
  };

  const renderOptimizedLayout = () => (
    <div className="h-full flex flex-col bg-background layout-optimized">
      {/* Compact Header */}
      <div className="border-b bg-background/95 backdrop-blur compact-header">
        <div className="flex items-center justify-between px-2 py-1">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="h-7 w-7 p-0"
            >
              <Sidebar className="h-3 w-3" />
            </Button>
            <span className="font-medium text-sm">PDF Viewer</span>
          </div>
          
          <div className="flex items-center gap-1">
            <Button variant="ghost" size="sm" className="h-7 px-2 text-xs">
              Page 1/10
            </Button>
            <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
              <Maximize2 className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Document Tabs */}
      <div className="border-b compact-tabs">
        <div className="flex items-center px-1">
          {DEMO_DOCUMENTS.slice(0, 3).map((doc) => (
            <div
              key={doc.id}
              className={cn(
                "flex items-center gap-1 px-2 py-1 text-xs border-b-2 cursor-pointer transition-colors",
                doc.id === activeDocument
                  ? "border-primary bg-background text-foreground"
                  : "border-transparent bg-muted/50 text-muted-foreground hover:bg-muted"
              )}
              onClick={() => setActiveDocument(doc.id)}
            >
              <div className="w-2 h-2 bg-green-500 rounded-full" />
              <span className="truncate max-w-[80px]">{doc.title}</span>
            </div>
          ))}
          <Button variant="ghost" size="sm" className="h-6 px-2 text-xs">
            +1
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex min-h-0">
        {/* Optimized Sidebar */}
        {sidebarOpen && (
          <div className="w-48 border-r bg-background compact-sidebar">
            <div className="p-2 border-b">
              <div className="text-xs font-medium">Tools</div>
            </div>
            <div className="p-2 space-y-1">
              <div className="text-xs text-muted-foreground">Outline</div>
              <div className="text-xs text-muted-foreground">Search</div>
              <div className="text-xs text-muted-foreground">Bookmarks</div>
            </div>
          </div>
        )}

        {/* PDF Display Area */}
        <div className="flex-1 pdf-display-optimized">
          <div className="h-full flex items-center justify-center bg-muted/20">
            <div className="text-center">
              <div className="w-32 h-40 bg-white border shadow-sm mx-auto mb-2 flex items-center justify-center">
                <span className="text-xs text-muted-foreground">PDF Content</span>
              </div>
              <div className="text-xs text-muted-foreground">
                Layout Mode: {layoutMode}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Compact Status Bar */}
      <div className="border-t px-2 py-1 bg-muted/20 text-xs text-muted-foreground">
        <div className="flex items-center justify-between">
          <span>4 documents open</span>
          <span>Page 1/10 • 100%</span>
        </div>
      </div>
    </div>
  );

  const renderLegacyLayout = () => (
    <div className="h-full flex flex-col bg-background">
      {/* Legacy Header */}
      <div className="border-b bg-background p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm">
              <Sidebar className="h-4 w-4" />
            </Button>
            <h1 className="font-semibold text-lg">PDF Viewer</h1>
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm">
              Page 1/10
            </Button>
            <Button variant="ghost" size="sm">
              <Maximize2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Legacy Document Tabs */}
      <div className="border-b p-2">
        <div className="flex items-center gap-2">
          {DEMO_DOCUMENTS.slice(0, 2).map((doc) => (
            <div
              key={doc.id}
              className="flex items-center gap-2 px-4 py-2 text-sm border-b-2 border-primary bg-background"
            >
              <div className="w-3 h-3 bg-green-500 rounded-full" />
              <span>{doc.title}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-4">
        <div className="h-full flex items-center justify-center bg-muted/20 rounded">
          <div className="text-center">
            <div className="w-48 h-64 bg-white border shadow mx-auto mb-4 flex items-center justify-center">
              <span className="text-muted-foreground">PDF Content</span>
            </div>
            <div className="text-muted-foreground">Legacy Layout</div>
          </div>
        </div>
      </div>

      {/* Legacy Status Bar */}
      <div className="border-t px-4 py-3 bg-muted/30">
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <span>2 documents open</span>
          <span>Page 1 of 10 • 100%</span>
        </div>
      </div>
    </div>
  );

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Layout Optimization Demo</h2>
          <p className="text-muted-foreground">
            Compare the optimized layout with the original design
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant={activeDemo === 'before' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setActiveDemo('before')}
          >
            Before
          </Button>
          <Button
            variant={activeDemo === 'after' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setActiveDemo('after')}
          >
            After
          </Button>
        </div>
      </div>

      <Tabs defaultValue="demo" className="space-y-4">
        <TabsList>
          <TabsTrigger value="demo">Interactive Demo</TabsTrigger>
          <TabsTrigger value="optimizations">Optimizations</TabsTrigger>
          <TabsTrigger value="metrics">Performance Metrics</TabsTrigger>
        </TabsList>

        <TabsContent value="demo" className="space-y-4">
          {/* Controls */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Demo Controls</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Viewport:</span>
                  <Button
                    variant={viewportSize === 'desktop' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewportSize('desktop')}
                  >
                    <Monitor className="h-4 w-4 mr-1" />
                    Desktop
                  </Button>
                  <Button
                    variant={viewportSize === 'tablet' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewportSize('tablet')}
                  >
                    <Tablet className="h-4 w-4 mr-1" />
                    Tablet
                  </Button>
                  <Button
                    variant={viewportSize === 'mobile' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewportSize('mobile')}
                  >
                    <Smartphone className="h-4 w-4 mr-1" />
                    Mobile
                  </Button>
                </div>

                <Separator orientation="vertical" className="h-6" />

                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Layout:</span>
                  <Button
                    variant={layoutMode === 'single' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setLayoutMode('single')}
                  >
                    <Maximize2 className="h-4 w-4 mr-1" />
                    Single
                  </Button>
                  <Button
                    variant={layoutMode === 'side-by-side' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setLayoutMode('side-by-side')}
                  >
                    <Split className="h-4 w-4 mr-1" />
                    Side-by-Side
                  </Button>
                  <Button
                    variant={layoutMode === 'grid' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setLayoutMode('grid')}
                  >
                    <Grid3X3 className="h-4 w-4 mr-1" />
                    Grid
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Demo Viewport */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Layout className="h-5 w-5" />
                {activeDemo === 'after' ? 'Optimized Layout' : 'Original Layout'}
                <Badge variant={activeDemo === 'after' ? 'default' : 'secondary'}>
                  {activeDemo === 'after' ? 'New' : 'Legacy'}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div
                className="border rounded-lg overflow-hidden bg-muted/50"
                style={{
                  ...getViewportDimensions(),
                  maxWidth: '100%',
                  aspectRatio: viewportSize === 'mobile' ? '9/16' : viewportSize === 'tablet' ? '3/4' : '16/10'
                }}
              >
                {activeDemo === 'after' ? renderOptimizedLayout() : renderLegacyLayout()}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="optimizations" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {optimizations.map((optimization, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-base">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    {optimization.title}
                  </CardTitle>
                  <CardDescription>{optimization.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <Badge variant="secondary">{optimization.improvement}</Badge>
                    <Badge variant="default">Implemented</Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="metrics" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Space Efficiency</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">+35%</div>
                <p className="text-sm text-muted-foreground">More content area</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Multi-Document Support</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">4x</div>
                <p className="text-sm text-muted-foreground">Simultaneous PDFs</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Responsive Breakpoints</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-600">5</div>
                <p className="text-sm text-muted-foreground">Optimized layouts</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
