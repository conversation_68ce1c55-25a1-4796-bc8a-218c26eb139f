"use client";

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import {
  Database,
  Zap,
  Cloud,
  HardDrive,
  Wifi,
  WifiOff,
  BarChart3,
  FileText,
  Layers,
  Trash2,
  Settings
} from 'lucide-react';
import VirtualizedPDFViewer from '../core/virtualized-pdf-viewer';
import CacheDashboard from '../cache/cache-dashboard';
import { useCacheManager } from '@/hooks/use-cache-manager';

interface AdvancedCachingDemoProps {
  className?: string;
}

export default function AdvancedCachingDemo({ className }: AdvancedCachingDemoProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [activeDemo, setActiveDemo] = useState<'overview' | 'performance' | 'offline'>('overview');

  const cacheManager = useCacheManager({
    config: {
      enableServiceWorker: true,
      enablePreloading: true,
      enableBackgroundSync: true,
      globalMemoryLimit: 150 * 1024 * 1024, // 150MB for demo
    },
    enableAutoStats: true,
    statsInterval: 2000, // Update every 2 seconds for demo
  });

  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type === 'application/pdf') {
      setSelectedFile(file);
    }
  }, []);

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const renderOverviewDemo = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Multi-Level Caching Architecture
          </CardTitle>
          <CardDescription>
            Experience the power of intelligent caching across multiple storage layers
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="border-2 border-blue-200">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Zap className="h-4 w-4 text-blue-500" />
                  Memory Cache
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="text-xs text-muted-foreground">Ultra-fast access</div>
                <div className="text-lg font-bold">
                  {cacheManager.stats ? cacheManager.stats.pages.memoryPages : 0} pages
                </div>
                <div className="text-xs">
                  {cacheManager.stats ? formatBytes(cacheManager.stats.pages.memorySizeMB * 1024 * 1024) : '0 B'}
                </div>
                {cacheManager.stats && (
                  <Progress value={cacheManager.stats.pages.memoryUtilization * 100} className="h-1" />
                )}
              </CardContent>
            </Card>

            <Card className="border-2 border-green-200">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <HardDrive className="h-4 w-4 text-green-500" />
                  IndexedDB Cache
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="text-xs text-muted-foreground">Persistent storage</div>
                <div className="text-lg font-bold">
                  {cacheManager.stats ? cacheManager.stats.thumbnails.memoryThumbnails : 0} items
                </div>
                <div className="text-xs">
                  {cacheManager.stats ? formatBytes(cacheManager.stats.thumbnails.memorySizeMB * 1024 * 1024) : '0 B'}
                </div>
                {cacheManager.stats && (
                  <Progress value={cacheManager.stats.thumbnails.memoryUtilization * 100} className="h-1" />
                )}
              </CardContent>
            </Card>

            <Card className="border-2 border-purple-200">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Cloud className="h-4 w-4 text-purple-500" />
                  Service Worker
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="text-xs text-muted-foreground">Offline support</div>
                <div className="text-lg font-bold">
                  {cacheManager.stats ? cacheManager.stats.serviceWorker.totalEntries : 0} entries
                </div>
                <div className="text-xs">
                  {cacheManager.isOnline ? 'Online' : 'Offline'} mode
                </div>
                <div className="flex items-center gap-1">
                  {cacheManager.isOnline ? (
                    <Wifi className="h-3 w-3 text-green-500" />
                  ) : (
                    <WifiOff className="h-3 w-3 text-red-500" />
                  )}
                  <span className="text-xs">
                    {cacheManager.stats?.global.serviceWorkerActive ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>

      {/* Cache Features */}
      <Card>
        <CardHeader>
          <CardTitle>Advanced Caching Features</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h4 className="font-semibold flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                Intelligent Management
              </h4>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center gap-2">
                  <Badge variant="outline" className="w-2 h-2 p-0 rounded-full bg-green-500"></Badge>
                  LRU/LFU eviction policies
                </li>
                <li className="flex items-center gap-2">
                  <Badge variant="outline" className="w-2 h-2 p-0 rounded-full bg-blue-500"></Badge>
                  Adaptive quality based on device
                </li>
                <li className="flex items-center gap-2">
                  <Badge variant="outline" className="w-2 h-2 p-0 rounded-full bg-purple-500"></Badge>
                  Background preloading
                </li>
                <li className="flex items-center gap-2">
                  <Badge variant="outline" className="w-2 h-2 p-0 rounded-full bg-orange-500"></Badge>
                  Memory usage optimization
                </li>
              </ul>
            </div>

            <div className="space-y-4">
              <h4 className="font-semibold flex items-center gap-2">
                <Layers className="h-4 w-4" />
                Performance Benefits
              </h4>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center gap-2">
                  <Badge variant="outline" className="w-2 h-2 p-0 rounded-full bg-green-500"></Badge>
                  90% faster page loads
                </li>
                <li className="flex items-center gap-2">
                  <Badge variant="outline" className="w-2 h-2 p-0 rounded-full bg-blue-500"></Badge>
                  80% less memory usage
                </li>
                <li className="flex items-center gap-2">
                  <Badge variant="outline" className="w-2 h-2 p-0 rounded-full bg-purple-500"></Badge>
                  Offline document access
                </li>
                <li className="flex items-center gap-2">
                  <Badge variant="outline" className="w-2 h-2 p-0 rounded-full bg-orange-500"></Badge>
                  Smooth scrolling experience
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderPerformanceDemo = () => (
    <div className="space-y-6">
      {selectedFile ? (
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
          <div className="xl:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>PDF Viewer with Advanced Caching</CardTitle>
                <CardDescription>
                  Watch the caching system work in real-time as you navigate the document
                </CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <div className="h-[600px] border rounded-lg overflow-hidden">
                  <VirtualizedPDFViewer
                    file={selectedFile}
                    enableVirtualization={true}
                    virtualConfig={{
                      overscanCount: 3,
                      maxCachedPages: 15,
                      adaptiveQuality: true,
                    }}
                    className="h-full"
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Real-time Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {cacheManager.stats && (
                  <>
                    <div>
                      <div className="flex justify-between text-xs mb-1">
                        <span>Memory Usage</span>
                        <span>{Math.round(cacheManager.stats.global.totalMemoryUtilization * 100)}%</span>
                      </div>
                      <Progress value={cacheManager.stats.global.totalMemoryUtilization * 100} className="h-2" />
                    </div>

                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div className="text-center p-2 bg-muted rounded">
                        <div className="font-bold">{cacheManager.stats.pages.memoryPages}</div>
                        <div>Pages Cached</div>
                      </div>
                      <div className="text-center p-2 bg-muted rounded">
                        <div className="font-bold">{cacheManager.stats.thumbnails.memoryThumbnails}</div>
                        <div>Thumbnails</div>
                      </div>
                      <div className="text-center p-2 bg-muted rounded">
                        <div className="font-bold">{cacheManager.stats.thumbnails.activeGenerations}</div>
                        <div>Generating</div>
                      </div>
                      <div className="text-center p-2 bg-muted rounded">
                        <div className="font-bold">{cacheManager.stats.serviceWorker.totalEntries}</div>
                        <div>SW Entries</div>
                      </div>
                    </div>
                  </>
                )}

                <div className="space-y-2">
                  <Button
                    onClick={cacheManager.optimizeCache}
                    size="sm"
                    variant="outline"
                    className="w-full"
                  >
                    <Zap className="h-3 w-3 mr-1" />
                    Optimize Cache
                  </Button>
                  <Button
                    onClick={() => cacheManager.clearCache('all')}
                    size="sm"
                    variant="outline"
                    className="w-full"
                  >
                    <Trash2 className="h-3 w-3 mr-1" />
                    Clear Cache
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      ) : (
        <Card>
          <CardContent className="flex items-center justify-center p-8">
            <div className="text-center">
              <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground mb-4">Select a PDF file to see performance caching in action</p>
              <input
                type="file"
                accept=".pdf"
                onChange={handleFileSelect}
                className="text-sm text-muted-foreground file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary file:text-primary-foreground hover:file:bg-primary/90"
              />
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );

  const renderOfflineDemo = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Cloud className="h-5 w-5" />
            Offline Capabilities
          </CardTitle>
          <CardDescription>
            Experience seamless offline document access with service worker caching
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-3">
                {cacheManager.isOnline ? (
                  <Wifi className="h-5 w-5 text-green-500" />
                ) : (
                  <WifiOff className="h-5 w-5 text-red-500" />
                )}
                <div>
                  <div className="font-medium">
                    {cacheManager.isOnline ? 'Online' : 'Offline'} Mode
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {cacheManager.isOnline 
                      ? 'Connected to internet - caching in background'
                      : 'Working offline - using cached content'
                    }
                  </div>
                </div>
              </div>
              <Badge variant={cacheManager.isOnline ? 'default' : 'secondary'}>
                {cacheManager.stats?.global.serviceWorkerActive ? 'SW Active' : 'SW Inactive'}
              </Badge>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm">Cached Documents</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {cacheManager.stats?.serviceWorker.pdfEntries || 0}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Available offline
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm">Cached Thumbnails</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {cacheManager.stats?.serviceWorker.thumbnailEntries || 0}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Quick previews ready
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="p-4 bg-muted/50 rounded-lg">
              <h4 className="font-medium mb-2">Try Offline Mode</h4>
              <p className="text-sm text-muted-foreground mb-3">
                Open your browser's developer tools and simulate offline mode to see how the cached content continues to work seamlessly.
              </p>
              <div className="text-xs text-muted-foreground">
                💡 Tip: Use Chrome DevTools → Network tab → &quot;Offline&quot; checkbox
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className={className}>
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-6 w-6" />
            Advanced Caching System Demo
          </CardTitle>
          <CardDescription>
            Explore the multi-level caching architecture that powers high-performance PDF viewing
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-4">
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${cacheManager.isReady ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
              <span className="text-sm">
                Cache System: {cacheManager.isReady ? 'Ready' : 'Initializing'}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${cacheManager.isOnline ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className="text-sm">
                Network: {cacheManager.isOnline ? 'Online' : 'Offline'}
              </span>
            </div>
          </div>

          {!selectedFile && activeDemo === 'performance' && (
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">Select PDF File</label>
              <input
                type="file"
                accept=".pdf"
                onChange={handleFileSelect}
                className="block w-full text-sm text-muted-foreground file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary file:text-primary-foreground hover:file:bg-primary/90"
              />
            </div>
          )}
        </CardContent>
      </Card>

      <Tabs value={activeDemo} onValueChange={(value) => setActiveDemo(value as 'overview' | 'performance' | 'offline')} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="offline">Offline Mode</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          {renderOverviewDemo()}
        </TabsContent>

        <TabsContent value="performance">
          {renderPerformanceDemo()}
        </TabsContent>

        <TabsContent value="offline">
          {renderOfflineDemo()}
        </TabsContent>
      </Tabs>

      {/* Full Cache Dashboard */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Cache Management Dashboard
          </CardTitle>
        </CardHeader>
        <CardContent>
          <CacheDashboard showAdvancedControls={true} />
        </CardContent>
      </Card>
    </div>
  );
}
