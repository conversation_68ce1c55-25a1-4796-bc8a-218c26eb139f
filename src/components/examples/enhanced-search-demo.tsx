"use client";

import React, { useState, use<PERSON><PERSON>back, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import {
  Search,
  Zap,
  Target,
  BarChart3,
  Filter,
  FileText,
  MessageSquare,
  Bookmark,
  FormInput,
  Database
} from 'lucide-react';
import { cn } from '@/lib/utils';
import AdvancedSearchInterface from '../search/advanced-search-interface';
import SearchResultsDisplay from '../search/search-results-display';
import SearchAnalytics from '../search/search-analytics';
import { useEnhancedSearch } from '@/hooks/use-enhanced-search';
import type { 
  AdvancedSearchQuery, 
  SearchResult 
} from '../search/advanced-search-interface';

interface EnhancedSearchDemoProps {
  className?: string;
}

// Mock search function that simulates real search behavior
const mockSearchFunction = async (query: AdvancedSearchQuery): Promise<SearchResult[]> => {
  // Simulate search delay
  await new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 200));

  const mockResults: SearchResult[] = [
    {
      id: '1',
      documentId: 'doc-1',
      title: 'Project Requirements Document',
      pageNumber: 1,
      content: `This document outlines the requirements for the new ${query.text} system. The system must be scalable, secure, and user-friendly.`,
      highlightedContent: `This document outlines the requirements for the new <mark>${query.text}</mark> system. The system must be scalable, secure, and user-friendly.`,
      context: 'Introduction section discussing system requirements and objectives.',
      type: 'text',
      relevanceScore: 0.95,
      position: { x: 100, y: 200, width: 400, height: 20 },
      author: 'John Doe',
      createdDate: new Date('2024-01-15'),
      modifiedDate: new Date('2024-01-20'),
      tags: ['requirements', 'project', 'system'],
      language: 'en',
    },
    {
      id: '2',
      documentId: 'doc-1',
      title: 'Project Requirements Document',
      pageNumber: 3,
      content: `The ${query.text} implementation should follow industry best practices and include comprehensive testing procedures.`,
      highlightedContent: `The <mark>${query.text}</mark> implementation should follow industry best practices and include comprehensive testing procedures.`,
      context: 'Implementation guidelines section with detailed technical specifications.',
      type: 'annotation',
      relevanceScore: 0.87,
      position: { x: 150, y: 300, width: 350, height: 15 },
      author: 'Jane Smith',
      createdDate: new Date('2024-01-16'),
      tags: ['implementation', 'testing', 'best-practices'],
      language: 'en',
    },
    {
      id: '3',
      documentId: 'doc-2',
      title: 'Technical Specifications',
      pageNumber: 5,
      content: `Key features of the ${query.text} include real-time processing, advanced analytics, and seamless integration capabilities.`,
      highlightedContent: `Key features of the <mark>${query.text}</mark> include real-time processing, advanced analytics, and seamless integration capabilities.`,
      context: 'Feature overview section detailing core system capabilities.',
      type: 'bookmark',
      relevanceScore: 0.82,
      position: { x: 80, y: 150, width: 450, height: 18 },
      author: 'Bob Johnson',
      createdDate: new Date('2024-01-18'),
      tags: ['features', 'analytics', 'integration'],
      language: 'en',
    },
    {
      id: '4',
      documentId: 'doc-3',
      title: 'User Manual',
      pageNumber: 2,
      content: `To configure ${query.text}, navigate to the settings panel and adjust the parameters according to your requirements.`,
      highlightedContent: `To configure <mark>${query.text}</mark>, navigate to the settings panel and adjust the parameters according to your requirements.`,
      context: 'Configuration section with step-by-step setup instructions.',
      type: 'form',
      relevanceScore: 0.78,
      position: { x: 120, y: 250, width: 380, height: 16 },
      author: 'Alice Wilson',
      createdDate: new Date('2024-01-19'),
      tags: ['configuration', 'setup', 'manual'],
      language: 'en',
    },
    {
      id: '5',
      documentId: 'doc-4',
      title: 'API Documentation',
      pageNumber: 8,
      content: `The ${query.text} API provides endpoints for data retrieval, processing, and management operations.`,
      highlightedContent: `The <mark>${query.text}</mark> API provides endpoints for data retrieval, processing, and management operations.`,
      context: 'API reference section with endpoint descriptions and examples.',
      type: 'metadata',
      relevanceScore: 0.74,
      position: { x: 90, y: 180, width: 420, height: 22 },
      author: 'Charlie Brown',
      createdDate: new Date('2024-01-21'),
      tags: ['api', 'documentation', 'endpoints'],
      language: 'en',
    },
  ];

  // Filter results based on query
  let filteredResults = mockResults;

  // Apply content type filters
  if (query.filters.contentTypes.length > 0) {
    filteredResults = filteredResults.filter(result => 
      query.filters.contentTypes.includes(result.type)
    );
  }

  // Apply author filters
  if (query.filters.authors.length > 0) {
    filteredResults = filteredResults.filter(result => 
      result.author && query.filters.authors.includes(result.author)
    );
  }

  // Apply tag filters
  if (query.filters.tags.length > 0) {
    filteredResults = filteredResults.filter(result => 
      query.filters.tags.some(tag => result.tags.includes(tag))
    );
  }

  // Apply page range filter
  if (query.filters.pageRange.start !== null || query.filters.pageRange.end !== null) {
    filteredResults = filteredResults.filter(result => {
      const start = query.filters.pageRange.start || 1;
      const end = query.filters.pageRange.end || Infinity;
      return result.pageNumber >= start && result.pageNumber <= end;
    });
  }

  // Apply date range filter
  if (query.filters.dateRange.start || query.filters.dateRange.end) {
    filteredResults = filteredResults.filter(result => {
      if (!result.createdDate) return false;
      const start = query.filters.dateRange.start || new Date(0);
      const end = query.filters.dateRange.end || new Date();
      return result.createdDate >= start && result.createdDate <= end;
    });
  }

  // Sort results
  filteredResults.sort((a, b) => {
    switch (query.options.sortBy) {
      case 'relevance':
        return query.options.sortOrder === 'asc' 
          ? a.relevanceScore - b.relevanceScore
          : b.relevanceScore - a.relevanceScore;
      case 'date':
        const aDate = a.createdDate || new Date(0);
        const bDate = b.createdDate || new Date(0);
        return query.options.sortOrder === 'asc'
          ? aDate.getTime() - bDate.getTime()
          : bDate.getTime() - aDate.getTime();
      case 'title':
        return query.options.sortOrder === 'asc'
          ? a.title.localeCompare(b.title)
          : b.title.localeCompare(a.title);
      case 'author':
        const aAuthor = a.author || '';
        const bAuthor = b.author || '';
        return query.options.sortOrder === 'asc'
          ? aAuthor.localeCompare(bAuthor)
          : bAuthor.localeCompare(aAuthor);
      default:
        return 0;
    }
  });

  // Limit results
  return filteredResults.slice(0, query.options.maxResults);
};

export default function EnhancedSearchDemo({ className }: EnhancedSearchDemoProps) {
  const [activeTab, setActiveTab] = useState<'search' | 'results' | 'analytics'>('search');
  const [selectedResult] = useState<SearchResult | null>(null);

  // Enhanced search hook
  const {
    query,
    results,
    isSearching,
    error,
    suggestions,
    searchHistory,
    analytics,
    clearCache,
    clearAnalytics,
    cacheSize,
    metricsCount,
  } = useEnhancedSearch(mockSearchFunction, {
    enableSuggestions: true,
    enableAnalytics: true,
    enableCaching: true,
    enableOCR: false,
    enableSemanticSearch: false,
    maxResults: 50,
    debounceDelay: 300,
    cacheTimeout: 5 * 60 * 1000,
  });

  // Mock suggestions
  const mockSuggestions = useMemo(() => [
    { type: 'recent' as const, text: 'project management', description: 'Recent search', frequency: 5 },
    { type: 'popular' as const, text: 'system requirements', description: 'Popular search', frequency: 12 },
    { type: 'smart' as const, text: 'author:"John Doe"', description: 'Search by author', category: 'author' },
    { type: 'autocomplete' as const, text: 'implementation guidelines', description: 'Autocomplete' },
    { type: 'contextual' as const, text: 'api documentation', description: 'From current context', category: 'document' },
  ], []);

  // Mock facets
  const mockFacets = useMemo(() => ({
    contentTypes: [
      { name: 'text', count: 15, selected: query.filters.contentTypes.includes('text') },
      { name: 'annotation', count: 8, selected: query.filters.contentTypes.includes('annotation') },
      { name: 'bookmark', count: 5, selected: query.filters.contentTypes.includes('bookmark') },
      { name: 'form', count: 3, selected: query.filters.contentTypes.includes('form') },
      { name: 'metadata', count: 2, selected: query.filters.contentTypes.includes('metadata') },
    ],
    authors: [
      { name: 'John Doe', count: 12, selected: query.filters.authors.includes('John Doe') },
      { name: 'Jane Smith', count: 8, selected: query.filters.authors.includes('Jane Smith') },
      { name: 'Bob Johnson', count: 6, selected: query.filters.authors.includes('Bob Johnson') },
      { name: 'Alice Wilson', count: 4, selected: query.filters.authors.includes('Alice Wilson') },
    ],
    tags: [
      { name: 'requirements', count: 10, selected: query.filters.tags.includes('requirements') },
      { name: 'implementation', count: 7, selected: query.filters.tags.includes('implementation') },
      { name: 'documentation', count: 5, selected: query.filters.tags.includes('documentation') },
      { name: 'api', count: 3, selected: query.filters.tags.includes('api') },
    ],
    dateRanges: [],
    languages: [
      { name: 'English', count: 25, selected: false },
      { name: 'Spanish', count: 3, selected: false },
      { name: 'French', count: 2, selected: false },
    ],
  }), [query.filters]);

  // Handle search
  const handleSearch = useCallback(async (searchQuery: AdvancedSearchQuery) => {
    return await mockSearchFunction(searchQuery);
  }, []);

  // Handle result selection
  const handleResultSelect = useCallback((result: SearchResult) => {
    setSelectedResult(result);
    setActiveTab('results');
  }, []);

  // Handle result actions
  const handleResultBookmark = useCallback((result: SearchResult) => {
    console.log('Bookmark result:', result);
  }, []);

  const handleResultShare = useCallback((result: SearchResult) => {
    console.log('Share result:', result);
  }, []);

  const handleResultDownload = useCallback((result: SearchResult) => {
    console.log('Download result:', result);
  }, []);

  // Handle save search
  const handleSaveSearch = useCallback((searchQuery: AdvancedSearchQuery, name: string) => {
    console.log('Save search:', name, searchQuery);
  }, []);

  // Export analytics data
  const handleExportAnalytics = useCallback(() => {
    const dataStr = JSON.stringify(analytics, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'search-analytics.json';
    link.click();
    URL.revokeObjectURL(url);
  }, [analytics]);

  return (
    <div className={cn("space-y-6", className)}>
      {/* Demo Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-6 w-6" />
            Enhanced Search UI Demo
          </CardTitle>
          <CardDescription>
            Experience advanced search capabilities with intelligent suggestions, comprehensive filtering, and real-time analytics
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4 text-green-500" />
              <div>
                <div className="text-sm font-medium">Smart Suggestions</div>
                <div className="text-xs text-muted-foreground">{suggestions.length} available</div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-blue-500" />
              <div>
                <div className="text-sm font-medium">Advanced Filters</div>
                <div className="text-xs text-muted-foreground">Multi-faceted search</div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4 text-purple-500" />
              <div>
                <div className="text-sm font-medium">Real-time Analytics</div>
                <div className="text-xs text-muted-foreground">{metricsCount} data points</div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-orange-500" />
              <div>
                <div className="text-sm font-medium">Performance Cache</div>
                <div className="text-xs text-muted-foreground">{cacheSize} cached queries</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Search Interface */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'search' | 'analytics' | 'settings')} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="search" className="flex items-center gap-2">
            <Search className="h-4 w-4" />
            Search
          </TabsTrigger>
          <TabsTrigger value="results" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Results ({results.length})
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="search" className="space-y-6">
          <AdvancedSearchInterface
            onSearch={handleSearch}
            onResultSelect={handleResultSelect}
            onSaveSearch={handleSaveSearch}
            initialQuery={query}
            suggestions={mockSuggestions}
            facets={mockFacets}
            recentSearches={searchHistory}
            savedSearches={[]}
          />

          {/* Quick Results Preview */}
          {results.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Quick Results Preview</CardTitle>
                <CardDescription>
                  Showing {Math.min(3, results.length)} of {results.length} results
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {results.slice(0, 3).map((result) => (
                    <div
                      key={result.id}
                      className="flex items-center gap-3 p-3 border rounded-lg cursor-pointer hover:bg-muted"
                      onClick={() => handleResultSelect(result)}
                    >
                      <div className="flex-shrink-0">
                        {result.type === 'text' && <FileText className="h-4 w-4 text-blue-500" />}
                        {result.type === 'annotation' && <MessageSquare className="h-4 w-4 text-green-500" />}
                        {result.type === 'bookmark' && <Bookmark className="h-4 w-4 text-yellow-500" />}
                        {result.type === 'form' && <FormInput className="h-4 w-4 text-purple-500" />}
                        {result.type === 'metadata' && <Database className="h-4 w-4 text-gray-500" />}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-sm truncate">{result.title}</span>
                          <Badge variant="outline" className="text-xs">Page {result.pageNumber}</Badge>
                        </div>
                        <div className="text-xs text-muted-foreground truncate">
                          {result.content.slice(0, 100)}...
                        </div>
                      </div>
                      <div className="flex items-center gap-1">
                        <div 
                          className={cn(
                            "w-2 h-2 rounded-full",
                            result.relevanceScore >= 0.8 ? "bg-green-500" :
                            result.relevanceScore >= 0.6 ? "bg-yellow-500" :
                            result.relevanceScore >= 0.4 ? "bg-orange-500" : "bg-red-500"
                          )}
                        />
                        <span className="text-xs text-muted-foreground">
                          {Math.round(result.relevanceScore * 100)}%
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
                {results.length > 3 && (
                  <div className="mt-3 pt-3 border-t">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setActiveTab('results')}
                      className="w-full"
                    >
                      View All {results.length} Results
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="results" className="space-y-6">
          <SearchResultsDisplay
            results={results}
            query={query.text}
            onResultSelect={handleResultSelect}
            onResultBookmark={handleResultBookmark}
            onResultShare={handleResultShare}
            onResultDownload={handleResultDownload}
            groupBy={query.options.groupBy}
            viewMode={viewMode}
            showPreviews={true}
          />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <SearchAnalytics
            data={analytics}
            onExportData={handleExportAnalytics}
            onClearData={clearAnalytics}
          />
        </TabsContent>
      </Tabs>

      {/* Status Bar */}
      <Card>
        <CardContent className="py-3">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-1">
                <div className={cn(
                  "w-2 h-2 rounded-full",
                  isSearching ? "bg-yellow-500 animate-pulse" : 
                  error ? "bg-red-500" : "bg-green-500"
                )} />
                <span className="text-muted-foreground">
                  {isSearching ? 'Searching...' : error ? 'Error' : 'Ready'}
                </span>
              </div>
              {query.text && (
                <div className="text-muted-foreground">
                  Query: &quot;{query.text}&quot; • {results.length} results
                </div>
              )}
            </div>
            <div className="flex items-center gap-4">
              <div className="text-muted-foreground">
                Cache: {cacheSize} queries
              </div>
              <div className="text-muted-foreground">
                Analytics: {analytics.totalSearches} searches
              </div>
              <Button variant="ghost" size="sm" onClick={clearCache}>
                Clear Cache
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
