"use client";

import React, { useCallback, useRef } from 'react';
import { Button } from '@/components/ui/button';

import {
  Grid3X3,
  Split,
  Maximize2,
  Layout,
  Layers,
  PictureInPicture,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuCheckboxItem,
} from '@/components/ui/dropdown-menu';

import { cn } from '@/lib/utils';
import type { DocumentInstance } from '@/lib/types/pdf';

export type LayoutMode = 
  | 'single'           // Single document view (default)
  | 'side-by-side'     // Two documents side by side
  | 'grid'             // Grid layout for multiple documents
  | 'split-horizontal' // Horizontal split
  | 'split-vertical'   // Vertical split
  | 'overlay'          // Overlay/comparison mode
  | 'picture-in-picture' // PiP mode
  | 'tabs-enhanced';   // Enhanced tabbed view

export interface LayoutConfig {
  mode: LayoutMode;
  gridColumns?: number;
  gridRows?: number;
  splitRatio?: number; // 0.1 to 0.9
  overlayOpacity?: number; // 0.1 to 1.0
  pipPosition?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  pipSize?: 'small' | 'medium' | 'large';
  showBorders?: boolean;
  syncScroll?: boolean;
  syncZoom?: boolean;
  autoArrange?: boolean;
}

interface MultiPDFLayoutManagerProps {
  documents: DocumentInstance[];
  activeDocumentId: string | null;
  layoutConfig: LayoutConfig;
  onLayoutConfigChange: (config: LayoutConfig) => void;
  onDocumentSelect: (documentId: string) => void;
  renderDocument: (document: DocumentInstance, containerProps?: object) => React.ReactNode;
  className?: string;
}



export default function MultiPDFLayoutManager({
  documents,
  activeDocumentId,
  layoutConfig,
  onLayoutConfigChange,
  onDocumentSelect,
  renderDocument,
  className
}: MultiPDFLayoutManagerProps) {

  const containerRef = useRef<HTMLDivElement>(null);

  const updateLayoutConfig = useCallback((updates: Partial<LayoutConfig>) => {
    onLayoutConfigChange({ ...layoutConfig, ...updates });
  }, [layoutConfig, onLayoutConfigChange]);

  const handleLayoutModeChange = useCallback((mode: LayoutMode) => {
    updateLayoutConfig({ mode });
  }, [updateLayoutConfig]);

  // Get visible documents based on layout mode
  const getVisibleDocuments = useCallback(() => {
    if (!documents.length) return [];
    
    switch (layoutConfig.mode) {
      case 'single':
        return activeDocumentId ? documents.filter(d => d.id === activeDocumentId) : [];
      
      case 'side-by-side':
      case 'split-horizontal':
      case 'split-vertical':
        return documents.slice(0, 2);
      
      case 'grid':
        const maxVisible = (layoutConfig.gridColumns || 2) * (layoutConfig.gridRows || 2);
        return documents.slice(0, maxVisible);
      
      case 'overlay':
        return documents.slice(0, 2); // Base + overlay
      
      case 'picture-in-picture':
        return documents.slice(0, 2); // Main + PiP
      
      case 'tabs-enhanced':
        return documents; // All documents available
      
      default:
        return documents.slice(0, 1);
    }
  }, [documents, activeDocumentId, layoutConfig.mode, layoutConfig.gridColumns, layoutConfig.gridRows]);

  // Render layout mode selector
  const renderLayoutSelector = () => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-8 px-2">
          <Layout className="h-4 w-4 mr-1" />
          Layout
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>Layout Mode</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        <DropdownMenuItem onClick={() => handleLayoutModeChange('single')}>
          <Maximize2 className="h-4 w-4 mr-2" />
          Single Document
        </DropdownMenuItem>
        
        <DropdownMenuItem 
          onClick={() => handleLayoutModeChange('side-by-side')}
          disabled={documents.length < 2}
        >
          <Split className="h-4 w-4 mr-2" />
          Side by Side
        </DropdownMenuItem>
        
        <DropdownMenuItem 
          onClick={() => handleLayoutModeChange('grid')}
          disabled={documents.length < 2}
        >
          <Grid3X3 className="h-4 w-4 mr-2" />
          Grid Layout
        </DropdownMenuItem>
        
        <DropdownMenuItem 
          onClick={() => handleLayoutModeChange('overlay')}
          disabled={documents.length < 2}
        >
          <Layers className="h-4 w-4 mr-2" />
          Overlay Compare
        </DropdownMenuItem>
        
        <DropdownMenuItem 
          onClick={() => handleLayoutModeChange('picture-in-picture')}
          disabled={documents.length < 2}
        >
          <PictureInPicture className="h-4 w-4 mr-2" />
          Picture in Picture
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        <DropdownMenuLabel>Options</DropdownMenuLabel>
        
        <DropdownMenuCheckboxItem
          checked={layoutConfig.showBorders}
          onCheckedChange={(checked) => updateLayoutConfig({ showBorders: checked })}
        >
          Show Borders
        </DropdownMenuCheckboxItem>
        
        <DropdownMenuCheckboxItem
          checked={layoutConfig.syncScroll}
          onCheckedChange={(checked) => updateLayoutConfig({ syncScroll: checked })}
        >
          Sync Scrolling
        </DropdownMenuCheckboxItem>
        
        <DropdownMenuCheckboxItem
          checked={layoutConfig.syncZoom}
          onCheckedChange={(checked) => updateLayoutConfig({ syncZoom: checked })}
        >
          Sync Zoom
        </DropdownMenuCheckboxItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );

  // Render single document layout
  const renderSingleLayout = (visibleDocuments: DocumentInstance[]) => {
    if (!visibleDocuments.length) return null;
    
    return (
      <div className="w-full h-full">
        {renderDocument(visibleDocuments[0], {
          className: "w-full h-full",
          onSelect: () => onDocumentSelect(visibleDocuments[0].id)
        })}
      </div>
    );
  };

  // Render side-by-side layout
  const renderSideBySideLayout = (visibleDocuments: DocumentInstance[]) => {
    if (visibleDocuments.length < 2) return renderSingleLayout(visibleDocuments);
    
    return (
      <div className="flex w-full h-full gap-1">
        {visibleDocuments.slice(0, 2).map((document, index) => (
          <div 
            key={document.id} 
            className={cn(
              "flex-1 min-w-0",
              layoutConfig.showBorders && "border rounded-lg overflow-hidden"
            )}
          >
            {renderDocument(document, {
              className: "w-full h-full",
              onSelect: () => onDocumentSelect(document.id),
              isActive: document.id === activeDocumentId
            })}
          </div>
        ))}
      </div>
    );
  };

  // Render grid layout with responsive scaling
  const renderGridLayout = (visibleDocuments: DocumentInstance[]) => {
    const columns = layoutConfig.gridColumns || 2;
    const rows = layoutConfig.gridRows || 2;

    // Calculate optimal scale based on grid size and viewport
    const getOptimalScale = () => {
      if (typeof window === 'undefined') return 0.7;

      const viewportWidth = window.innerWidth;
      const totalDocuments = visibleDocuments.length;

      // Desktop-specific scaling
      if (viewportWidth >= 2560) {
        return totalDocuments <= 4 ? 0.9 : totalDocuments <= 8 ? 0.75 : 0.6;
      } else if (viewportWidth >= 1920) {
        return totalDocuments <= 3 ? 0.85 : totalDocuments <= 6 ? 0.7 : 0.55;
      } else if (viewportWidth >= 1366) {
        return totalDocuments <= 2 ? 0.8 : totalDocuments <= 4 ? 0.65 : 0.5;
      } else {
        return totalDocuments <= 2 ? 0.7 : 0.5;
      }
    };

    return (
      <div
        className="grid w-full h-full multi-pdf-container"
        style={{
          gridTemplateColumns: `repeat(${columns}, 1fr)`,
          gridTemplateRows: `repeat(${rows}, 1fr)`,
          gap: '0.5rem'
        }}
      >
        {visibleDocuments.map((document, index) => (
          <div
            key={document.id}
            className={cn(
              "min-w-0 min-h-0 flex items-center justify-center",
              layoutConfig.showBorders && "border rounded-lg overflow-hidden shadow-sm"
            )}
          >
            {renderDocument(document, {
              className: "w-full h-full",
              onSelect: () => onDocumentSelect(document.id),
              isActive: document.id === activeDocumentId,
              scale: getOptimalScale()
            })}
          </div>
        ))}
      </div>
    );
  };

  // Render overlay layout
  const renderOverlayLayout = (visibleDocuments: DocumentInstance[]) => {
    if (visibleDocuments.length < 2) return renderSingleLayout(visibleDocuments);
    
    return (
      <div className="relative w-full h-full">
        {/* Base document */}
        <div className="absolute inset-0">
          {renderDocument(visibleDocuments[0], {
            className: "w-full h-full",
            onSelect: () => onDocumentSelect(visibleDocuments[0].id)
          })}
        </div>
        
        {/* Overlay document */}
        <div 
          className="absolute inset-0"
          style={{ opacity: layoutConfig.overlayOpacity || 0.7 }}
        >
          {renderDocument(visibleDocuments[1], {
            className: "w-full h-full",
            onSelect: () => onDocumentSelect(visibleDocuments[1].id)
          })}
        </div>
        
        {/* Overlay controls */}
        <div className="absolute top-2 right-2 bg-background/90 rounded-lg p-2 shadow-lg">
          <div className="flex items-center gap-2">
            <span className="text-xs">Opacity:</span>
            <input
              type="range"
              min="0.1"
              max="1"
              step="0.1"
              value={layoutConfig.overlayOpacity || 0.7}
              onChange={(e) => updateLayoutConfig({ overlayOpacity: parseFloat(e.target.value) })}
              className="w-16"
            />
          </div>
        </div>
      </div>
    );
  };

  // Main render method
  const renderLayout = () => {
    const visibleDocuments = getVisibleDocuments();
    
    if (!visibleDocuments.length) {
      return (
        <div className="flex items-center justify-center h-full text-muted-foreground">
          <div className="text-center">
            <Layout className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p>No documents to display</p>
          </div>
        </div>
      );
    }

    switch (layoutConfig.mode) {
      case 'single':
        return renderSingleLayout(visibleDocuments);
      case 'side-by-side':
      case 'split-horizontal':
      case 'split-vertical':
        return renderSideBySideLayout(visibleDocuments);
      case 'grid':
        return renderGridLayout(visibleDocuments);
      case 'overlay':
        return renderOverlayLayout(visibleDocuments);
      default:
        return renderSingleLayout(visibleDocuments);
    }
  };

  return (
    <TooltipProvider>
      <div ref={containerRef} className={cn("flex flex-col h-full", className)}>
        {/* Layout controls */}
        <div className="flex items-center justify-between p-2 border-b bg-muted/20">
          <div className="flex items-center gap-2">
            {renderLayoutSelector()}
            
            {layoutConfig.mode === 'grid' && (
              <div className="flex items-center gap-1 text-xs">
                <span>Grid:</span>
                <select
                  value={layoutConfig.gridColumns}
                  onChange={(e) => updateLayoutConfig({ gridColumns: parseInt(e.target.value) })}
                  className="w-12 h-6 text-xs border rounded px-1"
                >
                  <option value={1}>1</option>
                  <option value={2}>2</option>
                  <option value={3}>3</option>
                  <option value={4}>4</option>
                </select>
                <span>×</span>
                <select
                  value={layoutConfig.gridRows}
                  onChange={(e) => updateLayoutConfig({ gridRows: parseInt(e.target.value) })}
                  className="w-12 h-6 text-xs border rounded px-1"
                >
                  <option value={1}>1</option>
                  <option value={2}>2</option>
                  <option value={3}>3</option>
                  <option value={4}>4</option>
                </select>
              </div>
            )}
          </div>
          
          <div className="text-xs text-muted-foreground">
            {getVisibleDocuments().length} of {documents.length} documents visible
          </div>
        </div>

        {/* Layout content */}
        <div className="flex-1 min-h-0 p-1">
          {renderLayout()}
        </div>
      </div>
    </TooltipProvider>
  );
}
