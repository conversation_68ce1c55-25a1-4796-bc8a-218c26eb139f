"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { 
  HardDrive, 
  Zap, 
  Image, 
  Wifi, 
  WifiOff, 
  Trash2, 
  RefreshCw,
  BarChart3,
  Settings,
  Database,
  Cloud,
  Monitor
} from 'lucide-react';
import { useCacheManager } from '@/hooks/use-cache-manager';
import { useServiceWorker } from '@/lib/service-worker';

interface CacheDashboardProps {
  className?: string;
  showAdvancedControls?: boolean;
}

export default function CacheDashboard({ 
  className, 
  showAdvancedControls = false 
}: CacheDashboardProps) {
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [isClearingCache, setIsClearingCache] = useState(false);

  const cacheManager = useCacheManager({
    enableAutoStats: true,
    statsInterval: 5000, // Update every 5 seconds
  });

  const serviceWorker = useServiceWorker();

  const handleOptimizeCache = async () => {
    setIsOptimizing(true);
    try {
      await cacheManager.optimizeCache();
      await cacheManager.refreshStats();
    } catch (error) {
      console.error('Cache optimization failed:', error);
    } finally {
      setIsOptimizing(false);
    }
  };

  const handleClearCache = async (type: 'all' | 'pages' | 'thumbnails' | 'serviceWorker') => {
    setIsClearingCache(true);
    try {
      await cacheManager.clearCache(type);
      await cacheManager.refreshStats();
    } catch (error) {
      console.error('Cache clearing failed:', error);
    } finally {
      setIsClearingCache(false);
    }
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatPercentage = (value: number): string => {
    return `${Math.round(value * 100)}%`;
  };

  const renderOverviewTab = () => (
    <div className="space-y-6">
      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cache Status</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {cacheManager.isReady ? 'Ready' : 'Loading'}
            </div>
            <p className="text-xs text-muted-foreground">
              {cacheManager.isReady ? 'All systems operational' : 'Initializing...'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Network Status</CardTitle>
            {cacheManager.isOnline ? (
              <Wifi className="h-4 w-4 text-green-500" />
            ) : (
              <WifiOff className="h-4 w-4 text-red-500" />
            )}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {cacheManager.isOnline ? 'Online' : 'Offline'}
            </div>
            <p className="text-xs text-muted-foreground">
              {cacheManager.isOnline ? 'Connected to internet' : 'Working offline'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Service Worker</CardTitle>
            <Cloud className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {serviceWorker.status === 'registered' ? 'Active' : 'Inactive'}
            </div>
            <p className="text-xs text-muted-foreground">
              Background caching {serviceWorker.status === 'registered' ? 'enabled' : 'disabled'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Memory</CardTitle>
            <Monitor className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {cacheManager.stats ? formatBytes(cacheManager.stats.global.totalMemoryMB * 1024 * 1024) : '0 B'}
            </div>
            <p className="text-xs text-muted-foreground">
              {cacheManager.stats ? formatPercentage(cacheManager.stats.global.totalMemoryUtilization) : '0%'} utilized
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Memory Usage Progress */}
      {cacheManager.stats && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Memory Usage Breakdown
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span>Page Cache</span>
                <span>{formatBytes(cacheManager.stats.pages.memorySizeMB * 1024 * 1024)}</span>
              </div>
              <Progress value={cacheManager.stats.pages.memoryUtilization * 100} className="h-2" />
            </div>
            
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span>Thumbnail Cache</span>
                <span>{formatBytes(cacheManager.stats.thumbnails.memorySizeMB * 1024 * 1024)}</span>
              </div>
              <Progress value={cacheManager.stats.thumbnails.memoryUtilization * 100} className="h-2" />
            </div>
            
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span>Total Usage</span>
                <span>{formatBytes(cacheManager.stats.global.totalMemoryMB * 1024 * 1024)}</span>
              </div>
              <Progress value={cacheManager.stats.global.totalMemoryUtilization * 100} className="h-2" />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Manage cache performance and storage
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button
              onClick={handleOptimizeCache}
              disabled={isOptimizing || !cacheManager.isReady}
              variant="default"
              size="sm"
            >
              {isOptimizing ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Zap className="h-4 w-4 mr-2" />
              )}
              Optimize Cache
            </Button>

            <Button
              onClick={() => handleClearCache('all')}
              disabled={isClearingCache || !cacheManager.isReady}
              variant="outline"
              size="sm"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Clear All
            </Button>

            <Button
              onClick={cacheManager.refreshStats}
              disabled={!cacheManager.isReady}
              variant="outline"
              size="sm"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh Stats
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderDetailedTab = () => (
    <div className="space-y-6">
      {/* Page Cache Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <HardDrive className="h-5 w-5" />
            Page Cache
          </CardTitle>
          <CardDescription>
            PDF page rendering cache statistics
          </CardDescription>
        </CardHeader>
        <CardContent>
          {cacheManager.stats ? (
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div>
                <div className="text-2xl font-bold">{cacheManager.stats.pages.memoryPages}</div>
                <p className="text-xs text-muted-foreground">Pages in Memory</p>
              </div>
              <div>
                <div className="text-2xl font-bold">
                  {formatBytes(cacheManager.stats.pages.memorySizeMB * 1024 * 1024)}
                </div>
                <p className="text-xs text-muted-foreground">Memory Used</p>
              </div>
              <div>
                <div className="text-2xl font-bold">
                  {formatPercentage(cacheManager.stats.pages.memoryUtilization)}
                </div>
                <p className="text-xs text-muted-foreground">Utilization</p>
              </div>
            </div>
          ) : (
            <div className="text-center text-muted-foreground">Loading statistics...</div>
          )}
          
          <div className="mt-4 flex gap-2">
            <Button
              onClick={() => handleClearCache('pages')}
              disabled={isClearingCache}
              variant="outline"
              size="sm"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Clear Pages
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Thumbnail Cache Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Image className="h-5 w-5" />
            Thumbnail Cache
          </CardTitle>
          <CardDescription>
            PDF thumbnail cache statistics
          </CardDescription>
        </CardHeader>
        <CardContent>
          {cacheManager.stats ? (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <div className="text-2xl font-bold">{cacheManager.stats.thumbnails.memoryThumbnails}</div>
                <p className="text-xs text-muted-foreground">Thumbnails</p>
              </div>
              <div>
                <div className="text-2xl font-bold">
                  {formatBytes(cacheManager.stats.thumbnails.memorySizeMB * 1024 * 1024)}
                </div>
                <p className="text-xs text-muted-foreground">Memory Used</p>
              </div>
              <div>
                <div className="text-2xl font-bold">{cacheManager.stats.thumbnails.activeGenerations}</div>
                <p className="text-xs text-muted-foreground">Generating</p>
              </div>
              <div>
                <div className="text-2xl font-bold">{cacheManager.stats.thumbnails.queuedGenerations}</div>
                <p className="text-xs text-muted-foreground">Queued</p>
              </div>
            </div>
          ) : (
            <div className="text-center text-muted-foreground">Loading statistics...</div>
          )}
          
          <div className="mt-4 flex gap-2">
            <Button
              onClick={() => handleClearCache('thumbnails')}
              disabled={isClearingCache}
              variant="outline"
              size="sm"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Clear Thumbnails
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Service Worker Cache */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Cloud className="h-5 w-5" />
            Service Worker Cache
          </CardTitle>
          <CardDescription>
            Offline caching and background sync
          </CardDescription>
        </CardHeader>
        <CardContent>
          {cacheManager.stats ? (
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div>
                <div className="text-2xl font-bold">{cacheManager.stats.serviceWorker.pdfEntries}</div>
                <p className="text-xs text-muted-foreground">PDF Entries</p>
              </div>
              <div>
                <div className="text-2xl font-bold">{cacheManager.stats.serviceWorker.thumbnailEntries}</div>
                <p className="text-xs text-muted-foreground">Thumbnail Entries</p>
              </div>
              <div>
                <div className="text-2xl font-bold">{cacheManager.stats.serviceWorker.totalEntries}</div>
                <p className="text-xs text-muted-foreground">Total Entries</p>
              </div>
            </div>
          ) : (
            <div className="text-center text-muted-foreground">Loading statistics...</div>
          )}
          
          <div className="mt-4 flex gap-2">
            <Button
              onClick={() => handleClearCache('serviceWorker')}
              disabled={isClearingCache}
              variant="outline"
              size="sm"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Clear Service Worker Cache
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderSettingsTab = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Cache Settings
          </CardTitle>
          <CardDescription>
            Configure cache behavior and performance
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <Label htmlFor="auto-optimize">Auto Optimization</Label>
            <Switch id="auto-optimize" />
          </div>
          
          <div className="flex items-center justify-between">
            <Label htmlFor="preloading">Enable Preloading</Label>
            <Switch id="preloading" defaultChecked />
          </div>
          
          <div className="flex items-center justify-between">
            <Label htmlFor="background-sync">Background Sync</Label>
            <Switch id="background-sync" defaultChecked />
          </div>
          
          <div className="flex items-center justify-between">
            <Label htmlFor="service-worker">Service Worker</Label>
            <Switch 
              id="service-worker" 
              checked={serviceWorker.status === 'registered'}
              disabled
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );

  if (!cacheManager.isReady) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p className="text-muted-foreground">Initializing cache system...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={className}>
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="detailed">Detailed</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          {renderOverviewTab()}
        </TabsContent>

        <TabsContent value="detailed">
          {renderDetailedTab()}
        </TabsContent>

        <TabsContent value="settings">
          {renderSettingsTab()}
        </TabsContent>
      </Tabs>
    </div>
  );
}
